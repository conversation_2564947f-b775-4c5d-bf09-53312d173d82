#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装游戏宏工具所需的所有依赖
"""

import subprocess
import sys
import os
import time

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("    游戏宏工具 - 依赖安装脚本")
    print("=" * 60)
    print()

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    
    if version >= (3, 7):
        print(f"✓ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"✗ Python版本过低: {version.major}.{version.minor}")
        print("  需要Python 3.7或更高版本")
        return False

def install_package(package_name, description=""):
    """安装单个包"""
    print(f"\n正在安装 {package_name}...")
    if description:
        print(f"  说明: {description}")
    
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装包
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name, "--upgrade"
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✓ {package_name} 安装成功")
            return True
        else:
            print(f"✗ {package_name} 安装失败:")
            print(f"  错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ {package_name} 安装过程出错: {e}")
        return False

def install_all_dependencies():
    """安装所有依赖"""
    dependencies = [
        ("pynput", "用于监听和模拟键盘鼠标输入"),
        ("pywin32", "Windows API接口库"),
        ("requests", "HTTP请求库，用于检查更新"),
        ("psutil", "系统信息获取库")
    ]
    
    print("\n开始安装依赖库...")
    success_count = 0
    
    for package, description in dependencies:
        if install_package(package, description):
            success_count += 1
        time.sleep(1)  # 稍作延迟
    
    print(f"\n安装完成: {success_count}/{len(dependencies)} 个包安装成功")
    
    if success_count == len(dependencies):
        print("✓ 所有依赖安装成功!")
        return True
    else:
        print("⚠ 部分依赖安装失败，程序可能无法正常运行")
        return False

def test_imports():
    """测试导入"""
    print("\n测试依赖库导入...")
    
    test_packages = [
        ("pynput", "pynput.mouse"),
        ("pywin32", "win32api"),
        ("requests", "requests"),
        ("psutil", "psutil")
    ]
    
    success_count = 0
    
    for package_name, import_name in test_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"✗ {package_name} 导入失败: {e}")
        except Exception as e:
            print(f"⚠ {package_name} 导入测试出错: {e}")
    
    print(f"\n导入测试完成: {success_count}/{len(test_packages)} 个库可用")
    return success_count == len(test_packages)

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "游戏宏工具启动器.lnk")
        target = os.path.join(os.getcwd(), "启动器.bat")
        wDir = os.getcwd()
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = target
        shortcut.save()
        
        print(f"✓ 桌面快捷方式已创建: {path}")
        return True
        
    except Exception as e:
        print(f"⚠ 创建桌面快捷方式失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        print("\n请升级Python版本后重试")
        input("按回车键退出...")
        return
    
    # 安装依赖
    if install_all_dependencies():
        print("\n" + "=" * 60)
        print("    安装完成!")
        print("=" * 60)
        
        # 测试导入
        if test_imports():
            print("\n✓ 所有依赖库测试通过，可以正常使用!")
            
            # 尝试创建桌面快捷方式
            print("\n尝试创建桌面快捷方式...")
            create_desktop_shortcut()
            
            print("\n下一步:")
            print("1. 双击桌面上的'游戏宏工具启动器'快捷方式")
            print("2. 或者运行 启动器.bat 文件")
            print("3. 或者直接运行 python advanced_launcher.py")
            
        else:
            print("\n⚠ 部分依赖库测试失败，可能需要手动解决")
    else:
        print("\n" + "=" * 60)
        print("    安装失败!")
        print("=" * 60)
        print("\n可能的解决方案:")
        print("1. 以管理员身份运行此脚本")
        print("2. 检查网络连接")
        print("3. 手动安装: pip install pynput pywin32 requests psutil")
        print("4. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ 包名")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
