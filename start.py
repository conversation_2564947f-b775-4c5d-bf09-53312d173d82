#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能启动脚本
自动选择最合适的启动方式
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("    游戏宏工具 - 智能启动脚本")
    print("=" * 60)
    print()

def check_first_run():
    """检查是否首次运行"""
    config_file = "launcher_config.json"
    return not os.path.exists(config_file)

def check_dependencies():
    """检查依赖是否安装"""
    required_deps = ["pynput", "pywin32"]
    missing = []
    
    for dep in required_deps:
        try:
            __import__(dep.replace("-", "_"))
        except ImportError:
            missing.append(dep)
    
    return missing

def run_setup_wizard():
    """运行设置向导"""
    print("检测到首次运行，启动设置向导...")
    try:
        subprocess.run([sys.executable, "setup_wizard.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("设置向导运行失败")
        return False
    except FileNotFoundError:
        print("未找到设置向导文件")
        return False

def run_dependency_installer():
    """运行依赖安装器"""
    print("检测到缺失依赖，启动安装程序...")
    try:
        subprocess.run([sys.executable, "install_requirements.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("依赖安装失败")
        return False
    except FileNotFoundError:
        print("未找到依赖安装文件")
        return False

def run_advanced_launcher():
    """运行高级启动器"""
    print("启动高级启动器...")
    try:
        subprocess.run([sys.executable, "advanced_launcher.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("高级启动器运行失败")
        return False
    except FileNotFoundError:
        print("未找到高级启动器文件")
        return False

def run_smart_launcher():
    """运行智能启动器"""
    print("启动智能启动器...")
    try:
        subprocess.run([sys.executable, "smart_launcher.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("智能启动器运行失败")
        return False
    except FileNotFoundError:
        print("未找到智能启动器文件")
        return False

def run_main_program():
    """直接运行主程序"""
    print("直接启动主程序...")
    
    # 尝试从配置文件读取主程序路径
    main_program = "game_macro_gui.py"
    try:
        if os.path.exists("launcher_config.json"):
            with open("launcher_config.json", 'r', encoding='utf-8') as f:
                config = json.load(f)
                main_program = config.get("main_program", main_program)
    except:
        pass
    
    try:
        subprocess.run([sys.executable, main_program], check=True)
        return True
    except subprocess.CalledProcessError:
        print("主程序运行失败")
        return False
    except FileNotFoundError:
        print(f"未找到主程序文件: {main_program}")
        return False

def show_manual_instructions():
    """显示手动操作说明"""
    print("\n" + "=" * 60)
    print("    手动操作说明")
    print("=" * 60)
    print()
    print("如果自动启动失败，您可以尝试以下操作：")
    print()
    print("1. 安装依赖:")
    print("   python install_requirements.py")
    print("   或")
    print("   pip install pynput pywin32 requests psutil")
    print()
    print("2. 运行设置向导:")
    print("   python setup_wizard.py")
    print()
    print("3. 启动程序:")
    print("   python advanced_launcher.py  (推荐)")
    print("   或")
    print("   python smart_launcher.py")
    print("   或")
    print("   python game_macro_gui.py  (直接启动)")
    print()
    print("4. 以管理员身份运行:")
    print("   右键点击命令提示符 -> 以管理员身份运行")
    print("   然后执行上述命令")
    print()

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version_info.major}.{sys.version_info.minor}")
        input("按回车键退出...")
        return
    
    print(f"Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # 检查是否首次运行
    if check_first_run():
        print("检测到首次运行")
        
        # 检查是否有设置向导
        if os.path.exists("setup_wizard.py"):
            if run_setup_wizard():
                print("设置向导完成，重新启动...")
                # 递归调用自己
                subprocess.run([sys.executable, __file__])
                return
        else:
            print("未找到设置向导，跳过初始设置")
    
    # 检查依赖
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"缺少依赖: {', '.join(missing_deps)}")
        
        # 尝试自动安装
        if os.path.exists("install_requirements.py"):
            if run_dependency_installer():
                print("依赖安装完成，重新检查...")
                missing_deps = check_dependencies()
                if not missing_deps:
                    print("所有依赖已安装")
                else:
                    print(f"仍然缺少依赖: {', '.join(missing_deps)}")
        else:
            print("未找到依赖安装器")
    else:
        print("所有依赖已安装")
    
    # 选择启动器
    print("\n选择启动方式...")
    
    # 优先尝试高级启动器
    if os.path.exists("advanced_launcher.py"):
        print("发现高级启动器")
        if run_advanced_launcher():
            return
    
    # 尝试智能启动器
    if os.path.exists("smart_launcher.py"):
        print("发现智能启动器")
        if run_smart_launcher():
            return
    
    # 最后尝试直接运行主程序
    if run_main_program():
        return
    
    # 如果都失败了，显示手动说明
    print("\n所有自动启动方式都失败了")
    show_manual_instructions()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        show_manual_instructions()
        input("\n按回车键退出...")
