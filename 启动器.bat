@echo off
chcp 65001 >nul
title 游戏宏工具智能启动器

echo ========================================
echo    游戏宏工具智能启动器 v2.0
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    echo 如果已安装Python，请确保Python在系统PATH中
    pause
    exit /b 1
)

echo [信息] Python环境检查通过

:: 使用智能启动脚本
if exist "start.py" (
    echo [信息] 使用智能启动脚本...
    python start.py
) else if exist "advanced_launcher.py" (
    echo [信息] 启动高级启动器...
    python advanced_launcher.py
) else if exist "smart_launcher.py" (
    echo [信息] 启动智能启动器...
    python smart_launcher.py
) else if exist "game_macro_gui.py" (
    echo [信息] 直接启动主程序...
    python game_macro_gui.py
) else (
    echo [错误] 未找到任何可启动的文件
    echo 请确保以下文件之一存在:
    echo   - start.py
    echo   - advanced_launcher.py
    echo   - smart_launcher.py
    echo   - game_macro_gui.py
    pause
    exit /b 1
)

if %errorlevel% neq 0 (
    echo.
    echo [错误] 程序运行失败
    echo.
    echo 可能的解决方案:
    echo 1. 以管理员身份运行此批处理文件
    echo 2. 安装缺失的依赖: pip install pynput pywin32
    echo 3. 检查防病毒软件是否阻止了程序运行
    echo.
    pause
)
