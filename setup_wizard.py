#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动器设置向导
帮助用户完成初始配置
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import json
import subprocess
import sys
from pathlib import Path

class SetupWizard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("游戏宏工具启动器 - 设置向导")
        self.root.geometry("700x500")
        self.root.resizable(False, False)
        
        # 配置数据
        self.config = {
            "main_program": "game_macro_gui.py",
            "dependencies": ["pynput", "pywin32"],
            "python_min_version": [3, 7],
            "github_repo": "",
            "backup_url": "",
            "auto_check_updates": True,
            "auto_install_deps": True,
            "launch_as_admin": False,
            "check_interval": 3600,
            "last_check": 0
        }
        
        # 当前步骤
        self.current_step = 0
        self.steps = [
            "欢迎",
            "环境检查", 
            "依赖安装",
            "程序配置",
            "高级设置",
            "完成"
        ]
        
        self.create_widgets()
        self.show_step(0)
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题框架
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=20, pady=10)
        
        title_label = ttk.Label(title_frame, text="游戏宏工具启动器设置向导", 
                               font=("Arial", 16, "bold"))
        title_label.pack()
        
        # 进度条
        self.progress = ttk.Progressbar(title_frame, length=400, mode='determinate')
        self.progress.pack(pady=10)
        
        # 步骤指示器
        self.step_label = ttk.Label(title_frame, text="", font=("Arial", 10))
        self.step_label.pack()
        
        # 主内容框架
        self.content_frame = ttk.Frame(self.root)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.prev_btn = ttk.Button(button_frame, text="< 上一步", 
                                  command=self.prev_step, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT)
        
        self.next_btn = ttk.Button(button_frame, text="下一步 >", 
                                  command=self.next_step)
        self.next_btn.pack(side=tk.RIGHT)
        
        self.cancel_btn = ttk.Button(button_frame, text="取消", 
                                    command=self.cancel_setup)
        self.cancel_btn.pack(side=tk.RIGHT, padx=(0, 10))
    
    def clear_content(self):
        """清空内容区域"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_step(self, step):
        """显示指定步骤"""
        self.current_step = step
        self.progress['value'] = (step / (len(self.steps) - 1)) * 100
        self.step_label.config(text=f"步骤 {step + 1}/{len(self.steps)}: {self.steps[step]}")
        
        # 更新按钮状态
        self.prev_btn.config(state=tk.NORMAL if step > 0 else tk.DISABLED)
        
        if step == len(self.steps) - 1:
            self.next_btn.config(text="完成", command=self.finish_setup)
        else:
            self.next_btn.config(text="下一步 >", command=self.next_step)
        
        # 显示对应步骤的内容
        self.clear_content()
        
        if step == 0:
            self.show_welcome()
        elif step == 1:
            self.show_environment_check()
        elif step == 2:
            self.show_dependency_install()
        elif step == 3:
            self.show_program_config()
        elif step == 4:
            self.show_advanced_settings()
        elif step == 5:
            self.show_completion()
    
    def show_welcome(self):
        """显示欢迎页面"""
        welcome_text = """
欢迎使用游戏宏工具启动器设置向导！

本向导将帮助您：

🔧 检查系统环境
📦 安装必要的依赖库
⚙️ 配置程序设置
🚀 完成初始化设置

设置完成后，您将能够：
• 一键启动游戏宏工具
• 自动检测和修复问题
• 享受智能化的使用体验

点击"下一步"开始设置...
        """
        
        text_widget = tk.Text(self.content_frame, wrap=tk.WORD, height=15, 
                             font=("Arial", 11), state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, welcome_text)
        text_widget.config(state=tk.DISABLED)
    
    def show_environment_check(self):
        """显示环境检查页面"""
        ttk.Label(self.content_frame, text="正在检查系统环境...", 
                 font=("Arial", 12, "bold")).pack(pady=10)
        
        # 检查结果显示
        self.check_results = tk.Text(self.content_frame, height=12, state=tk.DISABLED)
        self.check_results.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 开始检查
        self.root.after(500, self.perform_environment_check)
    
    def perform_environment_check(self):
        """执行环境检查"""
        def add_result(message, status="INFO"):
            self.check_results.config(state=tk.NORMAL)
            self.check_results.insert(tk.END, f"[{status}] {message}\n")
            self.check_results.see(tk.END)
            self.check_results.config(state=tk.DISABLED)
            self.root.update()
        
        add_result("开始环境检查...")
        
        # 检查Python版本
        version = sys.version_info
        min_version = tuple(self.config["python_min_version"])
        
        if version >= min_version:
            add_result(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}", "OK")
        else:
            add_result(f"Python版本过低: {version.major}.{version.minor}, 需要 {min_version}", "ERROR")
        
        # 检查主程序文件
        if os.path.exists(self.config["main_program"]):
            add_result(f"主程序文件存在: {self.config['main_program']}", "OK")
        else:
            add_result(f"主程序文件不存在: {self.config['main_program']}", "WARNING")
        
        # 检查依赖库
        missing_deps = []
        for dep in self.config["dependencies"]:
            try:
                __import__(dep.replace("-", "_"))
                add_result(f"依赖库 {dep} 已安装", "OK")
            except ImportError:
                missing_deps.append(dep)
                add_result(f"依赖库 {dep} 未安装", "WARNING")
        
        # 检查管理员权限
        try:
            import ctypes
            if ctypes.windll.shell32.IsUserAnAdmin():
                add_result("当前具有管理员权限", "OK")
            else:
                add_result("当前没有管理员权限（建议以管理员身份运行）", "WARNING")
        except:
            add_result("无法检查管理员权限", "WARNING")
        
        add_result("环境检查完成")
        
        # 保存缺失的依赖信息
        self.missing_dependencies = missing_deps
    
    def show_dependency_install(self):
        """显示依赖安装页面"""
        ttk.Label(self.content_frame, text="依赖库安装", 
                 font=("Arial", 12, "bold")).pack(pady=10)
        
        if hasattr(self, 'missing_dependencies') and self.missing_dependencies:
            ttk.Label(self.content_frame, 
                     text=f"检测到缺失的依赖库: {', '.join(self.missing_dependencies)}").pack(pady=5)
            
            install_frame = ttk.Frame(self.content_frame)
            install_frame.pack(fill=tk.X, pady=10)
            
            self.install_btn = ttk.Button(install_frame, text="安装依赖库", 
                                         command=self.install_dependencies)
            self.install_btn.pack(side=tk.LEFT)
            
            self.install_status = ttk.Label(install_frame, text="")
            self.install_status.pack(side=tk.LEFT, padx=10)
            
        else:
            ttk.Label(self.content_frame, text="✓ 所有依赖库已安装", 
                     foreground="green").pack(pady=20)
        
        # 安装日志
        self.install_log = tk.Text(self.content_frame, height=10, state=tk.DISABLED)
        self.install_log.pack(fill=tk.BOTH, expand=True, pady=10)
    
    def install_dependencies(self):
        """安装依赖库"""
        self.install_btn.config(state=tk.DISABLED)
        self.install_status.config(text="正在安装...")
        
        def add_log(message):
            self.install_log.config(state=tk.NORMAL)
            self.install_log.insert(tk.END, message + "\n")
            self.install_log.see(tk.END)
            self.install_log.config(state=tk.DISABLED)
            self.root.update()
        
        success_count = 0
        for dep in self.missing_dependencies:
            add_log(f"正在安装 {dep}...")
            
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", dep, "--upgrade"
                ], capture_output=True, text=True, encoding='utf-8')
                
                if result.returncode == 0:
                    add_log(f"✓ {dep} 安装成功")
                    success_count += 1
                else:
                    add_log(f"✗ {dep} 安装失败: {result.stderr}")
                    
            except Exception as e:
                add_log(f"✗ {dep} 安装出错: {e}")
        
        if success_count == len(self.missing_dependencies):
            self.install_status.config(text="✓ 安装完成", foreground="green")
            self.missing_dependencies = []
        else:
            self.install_status.config(text="⚠ 部分安装失败", foreground="orange")
        
        self.install_btn.config(state=tk.NORMAL)
    
    def show_program_config(self):
        """显示程序配置页面"""
        ttk.Label(self.content_frame, text="程序配置", 
                 font=("Arial", 12, "bold")).pack(pady=10)
        
        # 主程序路径
        path_frame = ttk.Frame(self.content_frame)
        path_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(path_frame, text="主程序文件:").pack(anchor=tk.W)
        
        path_entry_frame = ttk.Frame(path_frame)
        path_entry_frame.pack(fill=tk.X, pady=5)
        
        self.program_path_var = tk.StringVar(value=self.config["main_program"])
        ttk.Entry(path_entry_frame, textvariable=self.program_path_var, 
                 width=50).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_entry_frame, text="浏览", 
                  command=self.browse_program).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 检查文件是否存在
        self.file_status = ttk.Label(path_frame, text="")
        self.file_status.pack(anchor=tk.W, pady=5)
        
        self.check_program_file()
        
        # 绑定路径变化事件
        self.program_path_var.trace('w', lambda *args: self.check_program_file())
    
    def browse_program(self):
        """浏览程序文件"""
        filename = filedialog.askopenfilename(
            filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")],
            title="选择主程序文件"
        )
        if filename:
            self.program_path_var.set(filename)
    
    def check_program_file(self):
        """检查程序文件"""
        path = self.program_path_var.get()
        if os.path.exists(path):
            self.file_status.config(text="✓ 文件存在", foreground="green")
        else:
            self.file_status.config(text="✗ 文件不存在", foreground="red")
    
    def show_advanced_settings(self):
        """显示高级设置页面"""
        ttk.Label(self.content_frame, text="高级设置", 
                 font=("Arial", 12, "bold")).pack(pady=10)
        
        # 自动化选项
        auto_frame = ttk.LabelFrame(self.content_frame, text="自动化选项")
        auto_frame.pack(fill=tk.X, pady=10)
        
        self.auto_check_var = tk.BooleanVar(value=self.config["auto_check_updates"])
        ttk.Checkbutton(auto_frame, text="启动时自动检查更新", 
                       variable=self.auto_check_var).pack(anchor=tk.W, padx=10, pady=5)
        
        self.auto_install_var = tk.BooleanVar(value=self.config["auto_install_deps"])
        ttk.Checkbutton(auto_frame, text="自动安装缺失的依赖", 
                       variable=self.auto_install_var).pack(anchor=tk.W, padx=10, pady=5)
        
        self.launch_admin_var = tk.BooleanVar(value=self.config["launch_as_admin"])
        ttk.Checkbutton(auto_frame, text="总是以管理员身份启动程序", 
                       variable=self.launch_admin_var).pack(anchor=tk.W, padx=10, pady=5)
        
        # 更新源设置
        update_frame = ttk.LabelFrame(self.content_frame, text="更新源设置（可选）")
        update_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(update_frame, text="GitHub仓库 (格式: 用户名/仓库名):").pack(anchor=tk.W, padx=10, pady=5)
        self.github_var = tk.StringVar(value=self.config["github_repo"])
        ttk.Entry(update_frame, textvariable=self.github_var, width=50).pack(fill=tk.X, padx=10, pady=5)
    
    def show_completion(self):
        """显示完成页面"""
        ttk.Label(self.content_frame, text="设置完成！", 
                 font=("Arial", 14, "bold"), foreground="green").pack(pady=20)
        
        completion_text = """
恭喜！启动器设置已完成。

您现在可以：

🚀 使用智能启动器启动程序
🔧 享受自动化的问题检测和修复
📊 查看详细的系统诊断信息
📥 自动检查和安装更新

启动方式：
• 双击 "启动器.bat" 文件
• 运行 "python advanced_launcher.py"
• 运行 "python smart_launcher.py"

感谢使用游戏宏工具启动器！
        """
        
        text_widget = tk.Text(self.content_frame, wrap=tk.WORD, height=12, 
                             font=("Arial", 10), state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, completion_text)
        text_widget.config(state=tk.DISABLED)
    
    def next_step(self):
        """下一步"""
        if self.current_step < len(self.steps) - 1:
            self.show_step(self.current_step + 1)
    
    def prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.show_step(self.current_step - 1)
    
    def cancel_setup(self):
        """取消设置"""
        if messagebox.askyesno("确认", "确定要取消设置吗？"):
            self.root.quit()
    
    def finish_setup(self):
        """完成设置"""
        # 保存配置
        self.config["main_program"] = self.program_path_var.get()
        self.config["auto_check_updates"] = self.auto_check_var.get()
        self.config["auto_install_deps"] = self.auto_install_var.get()
        self.config["launch_as_admin"] = self.launch_admin_var.get()
        self.config["github_repo"] = self.github_var.get()
        
        try:
            with open("launcher_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", "配置已保存！\n\n您现在可以使用启动器了。")
            self.root.quit()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def run(self):
        """运行向导"""
        self.root.mainloop()


def main():
    """主函数"""
    wizard = SetupWizard()
    wizard.run()


if __name__ == "__main__":
    main()
