import time
import json
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ctypes
import sys
from pynput import mouse, keyboard
from pynput.mouse import <PERSON><PERSON>, Controller as MouseController
from pynput.keyboard import Key, KeyCode, Controller as KeyboardController
import win32api
import win32con
from direct_input_helper import DirectInputHelper

# 自定义Windows低级输入模拟
user32 = ctypes.windll.user32
MOUSEEVENTF_MOVE = 0x0001
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
MOUSEEVENTF_RIGHTDOWN = 0x0008
MOUSEEVENTF_RIGHTUP = 0x0010
MOUSEEVENTF_MIDDLEDOWN = 0x0020
MOUSEEVENTF_MIDDLEUP = 0x0040
MOUSEEVENTF_WHEEL = 0x0800
MOUSEEVENTF_ABSOLUTE = 0x8000

class MacroRecorder:
    def __init__(self, status_callback=None):
        self.events = []
        self.recording = False
        self.start_time = 0
        self.keyboard_listener = None
        self.mouse_listener = None
        self.mouse_ctrl = MouseController()
        self.keyboard_ctrl = KeyboardController()
        # 增加使用DirectInput模式的标志
        self.use_direct_input = True
        # 状态更新回调
        self.status_callback = status_callback
        # 添加速度和循环次数属性
        self.speed = 1.0
        self.loop_count = 1
    
    def on_press(self, key):
        if not self.recording:
            return
        try:
            # 记录按键
            current_time = time.time() - self.start_time
            if hasattr(key, 'char'):
                if key.char is not None:
                    self.events.append({
                        'type': 'key_press',
                        'key': key.char,
                        'time': current_time
                    })
            else:
                # 特殊键
                key_name = str(key).replace('Key.', '')
                self.events.append({
                    'type': 'key_press',
                    'key': key_name,
                    'time': current_time
                })
                
                # 更新状态
                if self.status_callback:
                    self.status_callback(f"记录按键: {key_name}")
        except Exception as e:
            if self.status_callback:
                self.status_callback(f"按键记录错误: {e}")
    
    def on_release(self, key):
        if not self.recording:
            return
        try:
            current_time = time.time() - self.start_time
            if hasattr(key, 'char'):
                if key.char is not None:
                    self.events.append({
                        'type': 'key_release',
                        'key': key.char,
                        'time': current_time
                    })
            else:
                key_name = str(key).replace('Key.', '')
                self.events.append({
                    'type': 'key_release',
                    'key': key_name,
                    'time': current_time
                })
            
            # 检查停止录制的热键 (F8)
            if key == Key.f8:
                if self.status_callback:
                    self.status_callback("检测到F8键，停止录制")
                return False
        except Exception as e:
            if self.status_callback:
                self.status_callback(f"按键释放记录错误: {e}")
            
    def on_move(self, x, y):
        if not self.recording:
            return
        current_time = time.time() - self.start_time
        self.events.append({
            'type': 'mouse_move',
            'x': x,
            'y': y,
            'time': current_time
        })
        
    def on_click(self, x, y, button, pressed):
        if not self.recording:
            return
        current_time = time.time() - self.start_time
        btn = 'left'
        if button == Button.right:
            btn = 'right'
        elif button == Button.middle:
            btn = 'middle'
            
        action = 'press' if pressed else 'release'
        self.events.append({
            'type': f'mouse_{action}',
            'button': btn,
            'x': x,
            'y': y,
            'time': current_time
        })
        
        # 更新状态
        if self.status_callback:
            self.status_callback(f"记录鼠标{btn}键{action}，坐标: ({x}, {y})")
        
    def on_scroll(self, x, y, dx, dy):
        if not self.recording:
            return
        current_time = time.time() - self.start_time
        self.events.append({
            'type': 'mouse_scroll',
            'x': x,
            'y': y,
            'dx': dx,
            'dy': dy,
            'time': current_time
        })
        
    def start_recording(self):
        # 清除之前的记录
        self.events = []
        self.recording = True
        self.start_time = time.time()
        
        # 启动键盘和鼠标监听器
        self.keyboard_listener = keyboard.Listener(
            on_press=self.on_press,
            on_release=self.on_release)
        self.mouse_listener = mouse.Listener(
            on_move=self.on_move,
            on_click=self.on_click,
            on_scroll=self.on_scroll)
            
        self.keyboard_listener.start()
        self.mouse_listener.start()
        if self.status_callback:
            self.status_callback("开始录制 (按F8停止)...")
        
        # 使用线程等待录制完成
        def wait_for_recording():
            self.keyboard_listener.join()
            self.recording = False
            self.mouse_listener.stop()
            if self.status_callback:
                self.status_callback(f"录制完成, 共记录了 {len(self.events)} 个事件")
        
        threading.Thread(target=wait_for_recording, daemon=True).start()
        
    def save_macro(self, filename):
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.events, f, ensure_ascii=False, indent=2)
            if self.status_callback:
                self.status_callback(f"宏保存到 {filename}")
            return True
        except Exception as e:
            if self.status_callback:
                self.status_callback(f"保存宏出错: {e}")
            return False
        
    def load_macro(self, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.events = json.load(f)
            if self.status_callback:
                self.status_callback(f"从 {filename} 加载了 {len(self.events)} 个事件")
            return True
        except Exception as e:
            if self.status_callback:
                self.status_callback(f"加载宏出错: {e}")
            return False
    
    # 使用Windows API直接发送输入，更适合在游戏中使用
    def playback_macro(self, countdown_callback=None):
        if not self.events:
            if self.status_callback:
                self.status_callback("没有录制的宏可以回放")
            return
            
        if self.status_callback:
            self.status_callback("准备开始回放，3秒后开始...")
        
        # 倒计时
        for i in range(3, 0, -1):
            if self.status_callback:
                self.status_callback(f"{i}...")
            if countdown_callback:
                countdown_callback(i)
            time.sleep(1)
            
        if self.status_callback:
            self.status_callback("开始回放...")
        
        start_time = time.time()
        
        for _ in range(self.loop_count):
            if self.loop_count > 1 and self.status_callback and _ > 0:
                self.status_callback(f"开始第 {_+1} 次循环...")
            loop_start_time = time.time()
            for event in self.events:
                # 计算需要等待的时间
                current_time = time.time() - loop_start_time
                time_to_wait = event['time'] - current_time
                if time_to_wait > 0:
                    time.sleep(time_to_wait / self.speed)
                    
                # 根据事件类型执行相应的操作
                if event['type'] == 'key_press':
                    self._simulate_key(event['key'], True)
                elif event['type'] == 'key_release':
                    self._simulate_key(event['key'], False)
                elif event['type'] == 'mouse_move':
                    self._simulate_mouse_move(event['x'], event['y'])
                elif event['type'] == 'mouse_press':
                    self._simulate_mouse_button(event['button'], True, event['x'], event['y'])
                elif event['type'] == 'mouse_release':
                    self._simulate_mouse_button(event['button'], False, event['x'], event['y'])
                elif event['type'] == 'mouse_scroll':
                    self._simulate_mouse_scroll(event['dy'])
                    
        if self.status_callback:
            self.status_callback("宏回放完成")
    
    def _get_virtual_keycode(self, key):
        # 特殊键映射
        special_keys = {
            'alt': win32con.VK_MENU,
            'alt_l': win32con.VK_LMENU,
            'alt_r': win32con.VK_RMENU,
            'alt_gr': win32con.VK_RMENU,
            'backspace': win32con.VK_BACK,
            'caps_lock': win32con.VK_CAPITAL,
            'cmd': win32con.VK_LWIN,
            'cmd_l': win32con.VK_LWIN,
            'cmd_r': win32con.VK_RWIN,
            'ctrl': win32con.VK_CONTROL,
            'ctrl_l': win32con.VK_LCONTROL,
            'ctrl_r': win32con.VK_RCONTROL,
            'delete': win32con.VK_DELETE,
            'down': win32con.VK_DOWN,
            'end': win32con.VK_END,
            'enter': win32con.VK_RETURN,
            'esc': win32con.VK_ESCAPE,
            'f1': win32con.VK_F1,
            'f2': win32con.VK_F2,
            'f3': win32con.VK_F3,
            'f4': win32con.VK_F4,
            'f5': win32con.VK_F5,
            'f6': win32con.VK_F6,
            'f7': win32con.VK_F7,
            'f8': win32con.VK_F8,
            'f9': win32con.VK_F9,
            'f10': win32con.VK_F10,
            'f11': win32con.VK_F11,
            'f12': win32con.VK_F12,
            'home': win32con.VK_HOME,
            'insert': win32con.VK_INSERT,
            'left': win32con.VK_LEFT,
            'page_down': win32con.VK_NEXT,
            'page_up': win32con.VK_PRIOR,
            'right': win32con.VK_RIGHT,
            'shift': win32con.VK_SHIFT,
            'shift_l': win32con.VK_LSHIFT,
            'shift_r': win32con.VK_RSHIFT,
            'space': win32con.VK_SPACE,
            'tab': win32con.VK_TAB,
            'up': win32con.VK_UP
        }
        
        # 如果是特殊键
        if key in special_keys:
            return special_keys[key]
        # 如果是单个字符
        elif len(key) == 1:
            # 字母和数字
            return ord(key.upper())
        
        return None
        
    def _simulate_key(self, key, press):
        # 优先使用DirectInput方式
        if self.use_direct_input:
            if DirectInputHelper.send_key_event_direct(key, press):
                return
                
        # 如果DirectInput方式失败，回退到传统方式
        key_code = self._get_virtual_keycode(key)
        if key_code:
            if press:
                win32api.keybd_event(key_code, 0, 0, 0)
            else:
                win32api.keybd_event(key_code, 0, win32con.KEYEVENTF_KEYUP, 0)
    
    def _simulate_mouse_move(self, x, y):
        if self.use_direct_input:
            DirectInputHelper.set_cursor_position(x, y)
        else:
            # 将x,y坐标转换为屏幕绝对坐标
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)
            
            # 计算规范化的坐标 (0-65535)
            nx = int(65535 * x / screen_width)
            ny = int(65535 * y / screen_height)
            
            user32.mouse_event(MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_MOVE, nx, ny, 0, 0)
    
    def _simulate_mouse_button(self, button, press, x, y):
        if self.use_direct_input:
            DirectInputHelper.send_mouse_input_direct(x, y, button, press)
        else:
            # 首先移动鼠标到指定位置
            self._simulate_mouse_move(x, y)
            
            # 然后执行按钮操作
            if button == 'left':
                if press:
                    user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                else:
                    user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            elif button == 'right':
                if press:
                    user32.mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0)
                else:
                    user32.mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0)
            elif button == 'middle':
                if press:
                    user32.mouse_event(MOUSEEVENTF_MIDDLEDOWN, 0, 0, 0, 0)
                else:
                    user32.mouse_event(MOUSEEVENTF_MIDDLEUP, 0, 0, 0, 0)
    
    def _simulate_mouse_scroll(self, dy):
        user32.mouse_event(MOUSEEVENTF_WHEEL, 0, 0, int(dy * 120), 0)
        

class MacroRecorderGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        
        # 设置窗口
        self.title("游戏宏录制与回放工具")
        self.geometry("600x400")
        self.resizable(True, True)
        
        # 创建录制器实例
        self.recorder = MacroRecorder(self.update_status)
        
        # 创建界面
        self._create_widgets()
        
        # 窗口配置
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def _create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="控制")
        control_frame.pack(fill=tk.X, expand=False, pady=5)
        
        # 原有的按钮框架
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.TOP, fill=tk.X, pady=2)
        
        # 录制按钮
        self.record_btn = ttk.Button(button_frame, text="开始录制 (F8停止)", command=self.start_recording)
        self.record_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 回放按钮
        self.playback_btn = ttk.Button(button_frame, text="回放", command=self.start_playback)
        self.playback_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 保存按钮
        self.save_btn = ttk.Button(button_frame, text="保存宏", command=self.save_macro)
        self.save_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 加载按钮
        self.load_btn = ttk.Button(button_frame, text="加载宏", command=self.load_macro)
        self.load_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 切换输入模式按钮
        self.direct_input_var = tk.BooleanVar(value=self.recorder.use_direct_input)
        self.direct_input_cb = ttk.Checkbutton(
            button_frame, 
            text="使用DirectInput模式", 
            variable=self.direct_input_var,
            command=self.toggle_direct_input
        )
        self.direct_input_cb.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 新的设置框架
        settings_frame = ttk.Frame(control_frame)
        settings_frame.pack(side=tk.TOP, fill=tk.X, pady=2)
        
        # 速度调节
        speed_frame = ttk.Frame(settings_frame)
        speed_frame.pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Label(speed_frame, text="速度:").pack(side=tk.LEFT)
        self.speed_var = tk.DoubleVar(value=self.recorder.speed)
        speed_scale = ttk.Scale(speed_frame, from_=0.1, to=2.0, variable=self.speed_var, 
                                orient=tk.HORIZONTAL, length=100, command=self.update_speed)
        speed_scale.pack(side=tk.LEFT, padx=5)
        self.speed_label = ttk.Label(speed_frame, text=f"{self.recorder.speed}x")
        self.speed_label.pack(side=tk.LEFT)
        
        # 循环次数
        loop_frame = ttk.Frame(settings_frame)
        loop_frame.pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Label(loop_frame, text="循环次数:").pack(side=tk.LEFT)
        self.loop_var = tk.IntVar(value=self.recorder.loop_count)
        loop_entry = ttk.Entry(loop_frame, textvariable=self.loop_var, width=5)
        loop_entry.pack(side=tk.LEFT, padx=5)
        ttk.Button(loop_frame, text="设置", command=self.set_loop_count).pack(side=tk.LEFT)
        
        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 状态文本区域
        self.status_text = tk.Text(status_frame, wrap=tk.WORD, state=tk.DISABLED)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.status_text, orient=tk.VERTICAL, command=self.status_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.status_text.config(yscrollcommand=scrollbar.set)
        
        # 状态栏
        self.status_bar = ttk.Label(self, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 倒计时标签
        self.countdown_label = ttk.Label(self, text="", font=("Arial", 30))
        self.countdown_label.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
        self.countdown_label.lower()  # 放到底层
    
    def update_status(self, message):
        """更新状态文本区域"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
        self.status_bar.config(text=message)
        self.update()  # 刷新GUI
        
    def toggle_direct_input(self):
        """切换DirectInput模式"""
        self.recorder.use_direct_input = self.direct_input_var.get()
        mode = "DirectInput" if self.recorder.use_direct_input else "标准"
        self.update_status(f"输入模式切换为: {mode}")
    
    def start_recording(self):
        """开始录制"""
        self.update_status("准备开始录制...")
        self.record_btn.config(state=tk.DISABLED)
        
        # 倒计时
        for i in range(3, 0, -1):
            self.countdown_label.config(text=str(i))
            self.countdown_label.lift()  # 提升到顶层
            self.update()
            time.sleep(1)
        self.countdown_label.config(text="")
        
        # 开始录制
        self.recorder.start_recording()
        
        # 启动检查录制状态的定时器
        self.check_recording_status()
    
    def check_recording_status(self):
        """检查录制状态"""
        if self.recorder.recording:
            self.after(1000, self.check_recording_status)
        else:
            self.record_btn.config(state=tk.NORMAL)
    
    def start_playback(self):
        """开始回放"""
        if not self.recorder.events:
            messagebox.showwarning("警告", "没有录制的宏可以回放")
            return
        
        self.playback_btn.config(state=tk.DISABLED)
        
        # 启动回放线程
        def playback_thread():
            self.recorder.playback_macro(self.show_countdown)
            self.after(0, lambda: self.playback_btn.config(state=tk.NORMAL))
        
        threading.Thread(target=playback_thread, daemon=True).start()
    
    def show_countdown(self, count):
        """显示倒计时"""
        self.countdown_label.config(text=str(count))
        self.countdown_label.lift()
        if count == 1:  # 最后一秒后清除
            self.after(1000, lambda: self.countdown_label.config(text=""))
    
    def update_speed(self, value):
        """更新回放速度"""
        speed = round(float(value), 1)
        self.speed_var.set(speed)
        self.speed_label.config(text=f"{speed}x")
        self.recorder.speed = speed
        self.update_status(f"回放速度设置为 {speed}x")
    
    def set_loop_count(self):
        """设置循环次数"""
        try:
            count = int(self.loop_var.get())
            if count < 1:
                raise ValueError("循环次数必须大于0")
            self.recorder.loop_count = count
            self.update_status(f"循环次数设置为 {count} 次")
        except ValueError as e:
            messagebox.showerror("错误", f"无效的循环次数: {e}")
            self.loop_var.set(self.recorder.loop_count)
    
    def save_macro(self):
        """保存宏"""
        if not self.recorder.events:
            messagebox.showwarning("警告", "没有录制的宏可以保存")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON 文件", "*.json"), ("所有文件", "*.*")],
            title="保存宏"
        )
        
        if filename:
            if self.recorder.save_macro(filename):
                messagebox.showinfo("成功", f"宏已保存到 {filename}")
    
    def load_macro(self):
        """加载宏"""
        filename = filedialog.askopenfilename(
            defaultextension=".json",
            filetypes=[("JSON 文件", "*.json"), ("所有文件", "*.*")],
            title="加载宏"
        )
        
        if filename:
            if self.recorder.load_macro(filename):
                messagebox.showinfo("成功", f"已从 {filename} 加载宏")
    
    def on_closing(self):
        """窗口关闭处理"""
        if self.recorder.recording:
            if messagebox.askyesno("确认", "正在录制中，确定要退出吗？"):
                self.recorder.recording = False
                self.destroy()
        else:
            self.destroy()


if __name__ == "__main__":
    try:
        app = MacroRecorderGUI()
        app.mainloop()
    except Exception as e:
        messagebox.showerror("错误", f"程序发生错误: {e}")
        print(f"发生错误: {e}")