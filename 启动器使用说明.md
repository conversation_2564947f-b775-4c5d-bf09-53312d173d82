# 游戏宏工具智能启动器使用说明

## 概述

这个智能启动器系统为您的游戏宏工具提供了完整的自动化解决方案，包括：

- 🔍 **自动检测系统环境**
- 🔧 **自动安装和修复依赖**
- 🚀 **智能启动程序**
- 📊 **实时错误诊断**
- 💡 **智能解决方案建议**
- 📥 **自动更新检查**

## 快速开始

### 方法一：一键安装（推荐）

1. **双击运行** `install_requirements.py`
   - 自动检查Python环境
   - 自动安装所有依赖
   - 创建桌面快捷方式

2. **启动程序**
   - 双击桌面快捷方式
   - 或双击 `启动器.bat`

### 方法二：手动启动

1. **安装依赖**（如果还没安装）
   ```bash
   pip install pynput pywin32 requests psutil
   ```

2. **启动高级启动器**
   ```bash
   python advanced_launcher.py
   ```

## 启动器功能详解

### 🎯 智能启动器 (smart_launcher.py)

**基础功能：**
- ✅ 系统环境检查
- ✅ 依赖库自动安装
- ✅ 程序启动和监控
- ✅ 错误检测和解决方案
- ✅ 管理员权限管理

**适用场景：** 日常使用，简单直接

### 🚀 高级启动器 (advanced_launcher.py)

**高级功能：**
- 📋 **多标签界面**：主页、设置、诊断、更新
- 🔧 **自动修复**：一键解决常见问题
- 📊 **系统诊断**：详细的系统信息收集
- 📥 **更新管理**：GitHub集成，自动检查更新
- 💾 **备份功能**：程序文件备份
- 📝 **诊断报告**：详细的错误报告导出
- ⚙️ **高级设置**：自定义配置选项

**适用场景：** 高级用户，需要详细控制和诊断

## 界面说明

### 主页标签
- **快速状态**：显示Python、依赖、程序、权限、更新状态
- **快速操作**：一键检查、修复、启动、停止等
- **运行日志**：实时显示操作日志和错误信息

### 设置标签
- **程序设置**：配置主程序路径
- **自动化设置**：自动检查更新、自动安装依赖等
- **更新源设置**：GitHub仓库、备用下载链接

### 诊断标签
- **系统信息**：详细的硬件和软件信息
- **诊断工具**：依赖测试、权限检查、网络测试

### 更新标签
- **更新信息**：显示最新版本信息
- **更新控制**：检查、下载、安装更新

## 常见问题解决

### ❓ 程序无法启动

**可能原因和解决方案：**

1. **Python环境问题**
   - 确保安装了Python 3.7+
   - 检查Python是否在系统PATH中

2. **依赖库缺失**
   - 点击"自动修复"按钮
   - 或手动运行：`pip install pynput pywin32`

3. **权限不足**
   - 点击"管理员重启"按钮
   - 或右键以管理员身份运行

### ❓ 依赖安装失败

**解决方案：**

1. **网络问题**
   ```bash
   # 使用国内镜像
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pynput pywin32
   ```

2. **权限问题**
   - 以管理员身份运行命令提示符
   - 或使用 `--user` 参数：`pip install --user pynput`

3. **编译问题**
   - 安装Microsoft Visual C++ Redistributable
   - 更新pip：`python -m pip install --upgrade pip`

### ❓ 程序运行出错

**诊断步骤：**

1. **查看错误日志**
   - 在启动器的运行日志中查看详细错误信息

2. **导出诊断报告**
   - 点击"导出报告"按钮
   - 将报告发送给技术支持

3. **常见错误解决**
   - `ModuleNotFoundError`：缺少依赖库
   - `PermissionError`：权限不足
   - `Access Denied`：被安全软件阻止

## 高级配置

### 自定义更新源

在设置标签中配置：

1. **GitHub仓库**
   - 格式：`用户名/仓库名`
   - 例如：`username/game-macro-tool`

2. **备用下载链接**
   - 直接下载链接
   - 用于GitHub无法访问时

### 自动化设置

- **启动时自动检查更新**：程序启动时自动检查新版本
- **自动安装缺失依赖**：发现缺失依赖时自动安装
- **总是以管理员身份启动**：避免权限问题

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `smart_launcher.py` | 基础智能启动器 |
| `advanced_launcher.py` | 高级启动器（推荐） |
| `install_requirements.py` | 依赖安装脚本 |
| `启动器.bat` | Windows批处理启动文件 |
| `launcher_config.json` | 启动器配置文件（自动生成） |

## 技术支持

如果遇到问题：

1. **导出诊断报告**
   - 在高级启动器中点击"导出报告"
   - 包含完整的系统信息和错误日志

2. **保存运行日志**
   - 点击"保存日志"按钮
   - 记录详细的操作过程

3. **联系支持**
   - 提供诊断报告和日志文件
   - 描述具体的问题和操作步骤

## 更新日志

### v2.0 (高级启动器)
- ✨ 新增多标签界面
- ✨ 新增自动更新功能
- ✨ 新增系统诊断工具
- ✨ 新增备份功能
- ✨ 新增详细配置选项

### v1.0 (基础启动器)
- ✨ 基础环境检查
- ✨ 依赖自动安装
- ✨ 程序启动监控
- ✨ 错误诊断和建议

---

**享受智能化的程序启动体验！** 🎉
