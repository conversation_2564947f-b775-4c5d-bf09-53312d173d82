# 游戏宏录制与回放工具

这是一个专为游戏设计的操作录制和回放工具。它使用Windows底层API直接捕获和模拟输入，确保在大多数游戏中都能有效工作。

## 功能特点

- 录制鼠标移动、点击和滚轮操作
- 录制键盘按键和释放操作
- 使用Windows底层API直接模拟输入，提高在游戏中的兼容性
- 保存和加载宏，方便重复使用
- 精确的时间控制，确保回放的准确性

## 使用方法

### 安装依赖

在使用前，请确保安装了必要的库：

```
pip install pynput pywin32
```

### 基本操作

1. **开始录制**：选择菜单中的"1"，然后按F8键停止录制
2. **回放录制的宏**：选择菜单中的"2"，程序会准确回放之前录制的操作
3. **保存宏**：选择菜单中的"3"，输入文件名保存当前录制的宏
4. **加载宏**：选择菜单中的"4"，输入文件名加载已保存的宏
5. **退出程序**：选择菜单中的"5"

## 注意事项

1. **管理员权限**：某些游戏可能需要以管理员权限运行此工具
2. **游戏兼容性**：虽然本工具使用底层API，但某些游戏的反作弊系统可能会阻止外部输入
3. **热键**：录制过程中按F8键可以停止录制
4. **回放准备**：回放开始前有3秒倒计时，请确保游戏窗口处于活动状态

## 常见问题解决

1. **如果在游戏中无效**：
   - 尝试以管理员权限运行此程序
   - 确保游戏允许外部输入
   - 如果是全屏游戏，尝试使用无边框窗口模式

2. **录制的宏不准确**：
   - 确保录制时不要进行过于复杂的操作
   - 避免过快的鼠标移动

3. **程序崩溃**：
   - 检查是否安装了最新版本的依赖库
   - 尝试减少录制的操作复杂度

## 技术说明

此工具使用以下技术实现游戏中的有效操作：

- 使用`win32api`和`user32.dll`直接调用Windows底层API
- 使用`MOUSEEVENTF_ABSOLUTE`确保鼠标移动的精确性
- 模拟DirectInput事件，这是大多数游戏使用的输入方式
- 精确的时间戳记录和回放 