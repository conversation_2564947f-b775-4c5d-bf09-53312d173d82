import ctypes
from ctypes import windll, Structure, c_long, c_ulong, c_short, byref

# DirectInput常量定义
MOUSEEVENTF_ABSOLUTE = 0x8000
MOUSEEVENTF_MOVE = 0x0001
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
MOUSEEVENTF_RIGHTDOWN = 0x0008
MOUSEEVENTF_RIGHTUP = 0x0010
MOUSEEVENTF_MIDDLEDOWN = 0x0020
MOUSEEVENTF_MIDDLEUP = 0x0040
MOUSEEVENTF_WHEEL = 0x0800

# 键盘扫描码常量
SCAN_ESC = 0x01
SCAN_1 = 0x02
SCAN_2 = 0x03
SCAN_3 = 0x04
SCAN_4 = 0x05
SCAN_5 = 0x06
SCAN_6 = 0x07
SCAN_7 = 0x08
SCAN_8 = 0x09
SCAN_9 = 0x0A
SCAN_0 = 0x0B
SCAN_MINUS = 0x0C
SCAN_EQUALS = 0x0D
SCAN_BACKSPACE = 0x0E
SCAN_TAB = 0x0F
SCAN_Q = 0x10
SCAN_W = 0x11
SCAN_E = 0x12
SCAN_R = 0x13
SCAN_T = 0x14
SCAN_Y = 0x15
SCAN_U = 0x16
SCAN_I = 0x17
SCAN_O = 0x18
SCAN_P = 0x19
SCAN_LBRACKET = 0x1A
SCAN_RBRACKET = 0x1B
SCAN_ENTER = 0x1C
SCAN_LCTRL = 0x1D
SCAN_A = 0x1E
SCAN_S = 0x1F
SCAN_D = 0x20
SCAN_F = 0x21
SCAN_G = 0x22
SCAN_H = 0x23
SCAN_J = 0x24
SCAN_K = 0x25
SCAN_L = 0x26
SCAN_SEMICOLON = 0x27
SCAN_APOSTROPHE = 0x28
SCAN_GRAVE = 0x29
SCAN_LSHIFT = 0x2A
SCAN_BACKSLASH = 0x2B
SCAN_Z = 0x2C
SCAN_X = 0x2D
SCAN_C = 0x2E
SCAN_V = 0x2F
SCAN_B = 0x30
SCAN_N = 0x31
SCAN_M = 0x32
SCAN_COMMA = 0x33
SCAN_PERIOD = 0x34
SCAN_SLASH = 0x35
SCAN_RSHIFT = 0x36
SCAN_MULTIPLY = 0x37
SCAN_LALT = 0x38
SCAN_SPACE = 0x39
SCAN_CAPITAL = 0x3A
SCAN_F1 = 0x3B
SCAN_F2 = 0x3C
SCAN_F3 = 0x3D
SCAN_F4 = 0x3E
SCAN_F5 = 0x3F
SCAN_F6 = 0x40
SCAN_F7 = 0x41
SCAN_F8 = 0x42
SCAN_F9 = 0x43
SCAN_F10 = 0x44
SCAN_NUMLOCK = 0x45
SCAN_SCROLL = 0x46
SCAN_NUMPAD7 = 0x47
SCAN_NUMPAD8 = 0x48
SCAN_NUMPAD9 = 0x49
SCAN_SUBTRACT = 0x4A
SCAN_NUMPAD4 = 0x4B
SCAN_NUMPAD5 = 0x4C
SCAN_NUMPAD6 = 0x4D
SCAN_ADD = 0x4E
SCAN_NUMPAD1 = 0x4F
SCAN_NUMPAD2 = 0x50
SCAN_NUMPAD3 = 0x51
SCAN_NUMPAD0 = 0x52
SCAN_DECIMAL = 0x53
SCAN_F11 = 0x57
SCAN_F12 = 0x58

# 创建扫描码映射字典
SCANCODE_MAP = {
    'a': SCAN_A, 'b': SCAN_B, 'c': SCAN_C, 'd': SCAN_D, 'e': SCAN_E,
    'f': SCAN_F, 'g': SCAN_G, 'h': SCAN_H, 'i': SCAN_I, 'j': SCAN_J,
    'k': SCAN_K, 'l': SCAN_L, 'm': SCAN_M, 'n': SCAN_N, 'o': SCAN_O,
    'p': SCAN_P, 'q': SCAN_Q, 'r': SCAN_R, 's': SCAN_S, 't': SCAN_T,
    'u': SCAN_U, 'v': SCAN_V, 'w': SCAN_W, 'x': SCAN_X, 'y': SCAN_Y, 'z': SCAN_Z,
    '0': SCAN_0, '1': SCAN_1, '2': SCAN_2, '3': SCAN_3, '4': SCAN_4,
    '5': SCAN_5, '6': SCAN_6, '7': SCAN_7, '8': SCAN_8, '9': SCAN_9,
    'space': SCAN_SPACE, 'enter': SCAN_ENTER, 'tab': SCAN_TAB, 
    'esc': SCAN_ESC, 'backspace': SCAN_BACKSPACE,
    'shift': SCAN_LSHIFT, 'shift_r': SCAN_RSHIFT,
    'ctrl': SCAN_LCTRL, 'alt': SCAN_LALT,
    'f1': SCAN_F1, 'f2': SCAN_F2, 'f3': SCAN_F3, 'f4': SCAN_F4,
    'f5': SCAN_F5, 'f6': SCAN_F6, 'f7': SCAN_F7, 'f8': SCAN_F8,
    'f9': SCAN_F9, 'f10': SCAN_F10, 'f11': SCAN_F11, 'f12': SCAN_F12
}

class POINT(Structure):
    _fields_ = [("x", c_long), ("y", c_long)]

class MouseInput(Structure):
    _fields_ = [
        ("dx", c_long),
        ("dy", c_long),
        ("mouseData", c_ulong),
        ("dwFlags", c_ulong),
        ("time", c_ulong),
        ("dwExtraInfo", ctypes.POINTER(c_ulong))
    ]

class KeybdInput(Structure):
    _fields_ = [
        ("wVk", c_ulong),
        ("wScan", c_ulong),
        ("dwFlags", c_ulong),
        ("time", c_ulong),
        ("dwExtraInfo", ctypes.POINTER(c_ulong))
    ]

class HardwareInput(Structure):
    _fields_ = [
        ("uMsg", c_ulong),
        ("wParamL", c_short),
        ("wParamH", c_short)
    ]

class InputUnion(ctypes.Union):
    _fields_ = [
        ("mi", MouseInput),
        ("ki", KeybdInput),
        ("hi", HardwareInput)
    ]

class Input(Structure):
    _fields_ = [
        ("type", c_ulong),
        ("ii", InputUnion)
    ]

# 用于DirectInput的关键常量
INPUT_MOUSE = 0
INPUT_KEYBOARD = 1
INPUT_HARDWARE = 2
KEYEVENTF_SCANCODE = 0x0008
KEYEVENTF_KEYUP = 0x0002

class DirectInputHelper:
    @staticmethod
    def get_cursor_position():
        pt = POINT()
        windll.user32.GetCursorPos(byref(pt))
        return (pt.x, pt.y)
    
    @staticmethod
    def set_cursor_position(x, y):
        windll.user32.SetCursorPos(x, y)
    
    @staticmethod
    def get_screen_size():
        return (windll.user32.GetSystemMetrics(0), windll.user32.GetSystemMetrics(1))
        
    @staticmethod
    def send_mouse_event(x, y, button_flags):
        # 将x,y坐标转换为屏幕绝对坐标
        screen_width, screen_height = DirectInputHelper.get_screen_size()
        
        # 计算规范化的坐标 (0-65535)
        nx = int(65535 * x / screen_width)
        ny = int(65535 * y / screen_height)
        
        windll.user32.mouse_event(MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_MOVE | button_flags, nx, ny, 0, 0)
    
    @staticmethod
    def send_key_event_direct(key, press=True):
        """
        使用DirectInput方式发送键盘事件，通过扫描码而非虚拟键码
        这种方式更容易被游戏检测到
        """
        # 获取键的扫描码
        scan_code = 0
        if key in SCANCODE_MAP:
            scan_code = SCANCODE_MAP[key]
        else:
            # 如果是单字符，尝试取第一个字符转小写
            if len(key) == 1:
                key_lower = key.lower()
                if key_lower in SCANCODE_MAP:
                    scan_code = SCANCODE_MAP[key_lower]
        
        if scan_code == 0:
            return False
        
        # 创建输入结构
        extra = ctypes.c_ulong(0)
        ii_ = InputUnion()
        ii_.ki = KeybdInput(0, scan_code, KEYEVENTF_SCANCODE | (KEYEVENTF_KEYUP if not press else 0), 0, ctypes.pointer(extra))
        x = Input(INPUT_KEYBOARD, ii_)
        
        # 发送输入
        windll.user32.SendInput(1, ctypes.byref(x), ctypes.sizeof(x))
        return True
    
    @staticmethod
    def send_mouse_input_direct(x, y, button, press):
        """使用DirectInput方式发送鼠标事件"""
        # 移动鼠标到指定位置
        DirectInputHelper.set_cursor_position(x, y)
        
        # 设置按钮标志
        flags = 0
        if button == 'left':
            flags = MOUSEEVENTF_LEFTDOWN if press else MOUSEEVENTF_LEFTUP
        elif button == 'right':
            flags = MOUSEEVENTF_RIGHTDOWN if press else MOUSEEVENTF_RIGHTUP
        elif button == 'middle':
            flags = MOUSEEVENTF_MIDDLEDOWN if press else MOUSEEVENTF_MIDDLEUP
            
        # 发送鼠标事件
        DirectInputHelper.send_mouse_event(x, y, flags) 