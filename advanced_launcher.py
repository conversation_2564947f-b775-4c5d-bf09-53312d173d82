#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级智能程序启动器
包含自动更新、错误诊断、系统优化等功能
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import time
import json
import requests
from pathlib import Path
import ctypes
import winreg
import shutil
import zipfile
import hashlib
from urllib.parse import urlparse
import webbrowser

class AdvancedLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("高级智能程序启动器 v2.0")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 配置文件
        self.config_file = "launcher_config.json"
        self.load_config()
        
        # 状态变量
        self.is_running = False
        self.process = None
        self.update_available = False
        
        self.create_widgets()
        self.auto_check_on_startup()
    
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "main_program": "game_macro_gui.py",
            "dependencies": ["pynput", "pywin32"],
            "python_min_version": [3, 7],
            "github_repo": "",  # 用户可以设置GitHub仓库
            "backup_url": "",   # 备用下载链接
            "auto_check_updates": True,
            "auto_install_deps": False,
            "launch_as_admin": False,
            "check_interval": 3600,  # 检查更新间隔(秒)
            "last_check": 0
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = {**default_config, **json.load(f)}
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            self.config = default_config
            print(f"配置文件加载失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 主页标签
        self.create_main_tab(notebook)
        
        # 设置标签
        self.create_settings_tab(notebook)
        
        # 诊断标签
        self.create_diagnostic_tab(notebook)
        
        # 更新标签
        self.create_update_tab(notebook)
        
        # 状态栏
        self.status_bar = ttk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_main_tab(self, notebook):
        """创建主页标签"""
        main_frame = ttk.Frame(notebook)
        notebook.add(main_frame, text="主页")
        
        # 标题
        title_label = ttk.Label(main_frame, text="游戏宏工具高级启动器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 快速状态框架
        quick_status_frame = ttk.LabelFrame(main_frame, text="快速状态")
        quick_status_frame.pack(fill=tk.X, pady=5)
        
        # 状态指示器
        self.status_vars = {
            "python": tk.StringVar(value="检查中..."),
            "dependencies": tk.StringVar(value="检查中..."),
            "program": tk.StringVar(value="检查中..."),
            "admin": tk.StringVar(value="检查中..."),
            "updates": tk.StringVar(value="检查中...")
        }
        
        status_items = [
            ("Python环境", "python", "green"),
            ("依赖库", "dependencies", "blue"), 
            ("主程序", "program", "purple"),
            ("管理员权限", "admin", "orange"),
            ("更新状态", "updates", "red")
        ]
        
        for i, (label, key, color) in enumerate(status_items):
            frame = ttk.Frame(quick_status_frame)
            frame.pack(fill=tk.X, padx=5, pady=2)
            ttk.Label(frame, text=f"{label}:", width=12).pack(side=tk.LEFT)
            status_label = ttk.Label(frame, textvariable=self.status_vars[key])
            status_label.pack(side=tk.LEFT)
        
        # 快速操作按钮
        quick_actions_frame = ttk.LabelFrame(main_frame, text="快速操作")
        quick_actions_frame.pack(fill=tk.X, pady=5)
        
        button_frame = ttk.Frame(quick_actions_frame)
        button_frame.pack(pady=5)
        
        # 第一行按钮
        row1 = ttk.Frame(button_frame)
        row1.pack(fill=tk.X, pady=2)
        
        self.check_btn = ttk.Button(row1, text="🔍 全面检查", 
                                   command=self.comprehensive_check)
        self.check_btn.pack(side=tk.LEFT, padx=5)
        
        self.auto_fix_btn = ttk.Button(row1, text="🔧 自动修复", 
                                      command=self.auto_fix_issues)
        self.auto_fix_btn.pack(side=tk.LEFT, padx=5)
        
        self.launch_btn = ttk.Button(row1, text="🚀 启动程序", 
                                    command=self.launch_program)
        self.launch_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(row1, text="⏹ 停止程序", 
                                  command=self.stop_program, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # 第二行按钮
        row2 = ttk.Frame(button_frame)
        row2.pack(fill=tk.X, pady=2)
        
        self.admin_btn = ttk.Button(row2, text="🛡 管理员重启", 
                                   command=self.restart_as_admin)
        self.admin_btn.pack(side=tk.LEFT, padx=5)
        
        self.update_btn = ttk.Button(row2, text="📥 检查更新", 
                                    command=self.check_for_updates)
        self.update_btn.pack(side=tk.LEFT, padx=5)
        
        self.backup_btn = ttk.Button(row2, text="💾 备份程序", 
                                    command=self.backup_program)
        self.backup_btn.pack(side=tk.LEFT, padx=5)
        
        self.help_btn = ttk.Button(row2, text="❓ 帮助", 
                                  command=self.show_help)
        self.help_btn.pack(side=tk.LEFT, padx=5)
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="运行日志")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, 
                                                 height=12, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Button(log_control_frame, text="清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=2)
        ttk.Button(log_control_frame, text="保存日志", 
                  command=self.save_log).pack(side=tk.LEFT, padx=2)
        ttk.Button(log_control_frame, text="导出报告", 
                  command=self.export_diagnostic_report).pack(side=tk.LEFT, padx=2)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5)
    
    def create_settings_tab(self, notebook):
        """创建设置标签"""
        settings_frame = ttk.Frame(notebook)
        notebook.add(settings_frame, text="设置")
        
        # 程序设置
        program_frame = ttk.LabelFrame(settings_frame, text="程序设置")
        program_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 主程序路径
        ttk.Label(program_frame, text="主程序文件:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.main_program_var = tk.StringVar(value=self.config["main_program"])
        ttk.Entry(program_frame, textvariable=self.main_program_var, width=40).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(program_frame, text="浏览", 
                  command=self.browse_main_program).grid(row=0, column=2, padx=5, pady=2)
        
        # 自动设置
        auto_frame = ttk.LabelFrame(settings_frame, text="自动化设置")
        auto_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.auto_check_var = tk.BooleanVar(value=self.config["auto_check_updates"])
        ttk.Checkbutton(auto_frame, text="启动时自动检查更新", 
                       variable=self.auto_check_var).pack(anchor=tk.W, padx=5, pady=2)
        
        self.auto_install_var = tk.BooleanVar(value=self.config["auto_install_deps"])
        ttk.Checkbutton(auto_frame, text="自动安装缺失的依赖", 
                       variable=self.auto_install_var).pack(anchor=tk.W, padx=5, pady=2)
        
        self.launch_admin_var = tk.BooleanVar(value=self.config["launch_as_admin"])
        ttk.Checkbutton(auto_frame, text="总是以管理员身份启动", 
                       variable=self.launch_admin_var).pack(anchor=tk.W, padx=5, pady=2)
        
        # 更新源设置
        update_frame = ttk.LabelFrame(settings_frame, text="更新源设置")
        update_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(update_frame, text="GitHub仓库:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.github_repo_var = tk.StringVar(value=self.config["github_repo"])
        ttk.Entry(update_frame, textvariable=self.github_repo_var, width=50).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(update_frame, text="备用下载链接:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.backup_url_var = tk.StringVar(value=self.config["backup_url"])
        ttk.Entry(update_frame, textvariable=self.backup_url_var, width=50).grid(row=1, column=1, padx=5, pady=2)
        
        # 保存设置按钮
        ttk.Button(settings_frame, text="保存设置", 
                  command=self.save_settings).pack(pady=10)
    
    def create_diagnostic_tab(self, notebook):
        """创建诊断标签"""
        diagnostic_frame = ttk.Frame(notebook)
        notebook.add(diagnostic_frame, text="诊断")
        
        # 系统信息
        system_frame = ttk.LabelFrame(diagnostic_frame, text="系统信息")
        system_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.system_info_text = scrolledtext.ScrolledText(system_frame, height=8, state=tk.DISABLED)
        self.system_info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 诊断工具
        tools_frame = ttk.LabelFrame(diagnostic_frame, text="诊断工具")
        tools_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tools_button_frame = ttk.Frame(tools_frame)
        tools_button_frame.pack(pady=5)
        
        ttk.Button(tools_button_frame, text="收集系统信息", 
                  command=self.collect_system_info).pack(side=tk.LEFT, padx=5)
        ttk.Button(tools_button_frame, text="测试依赖库", 
                  command=self.test_dependencies).pack(side=tk.LEFT, padx=5)
        ttk.Button(tools_button_frame, text="检查权限", 
                  command=self.check_permissions).pack(side=tk.LEFT, padx=5)
        ttk.Button(tools_button_frame, text="网络连接测试", 
                  command=self.test_network).pack(side=tk.LEFT, padx=5)
    
    def create_update_tab(self, notebook):
        """创建更新标签"""
        update_frame = ttk.Frame(notebook)
        notebook.add(update_frame, text="更新")
        
        # 更新信息
        update_info_frame = ttk.LabelFrame(update_frame, text="更新信息")
        update_info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.update_info_text = scrolledtext.ScrolledText(update_info_frame, height=8, state=tk.DISABLED)
        self.update_info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 更新控制
        update_control_frame = ttk.LabelFrame(update_frame, text="更新控制")
        update_control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        update_buttons_frame = ttk.Frame(update_control_frame)
        update_buttons_frame.pack(pady=5)
        
        self.check_update_btn = ttk.Button(update_buttons_frame, text="检查更新", 
                                          command=self.check_for_updates)
        self.check_update_btn.pack(side=tk.LEFT, padx=5)
        
        self.download_update_btn = ttk.Button(update_buttons_frame, text="下载更新", 
                                             command=self.download_update, state=tk.DISABLED)
        self.download_update_btn.pack(side=tk.LEFT, padx=5)
        
        self.install_update_btn = ttk.Button(update_buttons_frame, text="安装更新",
                                            command=self.install_update, state=tk.DISABLED)
        self.install_update_btn.pack(side=tk.LEFT, padx=5)

    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.status_bar.config(text=message)
        self.root.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            title="保存日志"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"日志已保存到: {filename}")
            except Exception as e:
                self.log_message(f"保存日志失败: {e}", "ERROR")

    def is_admin(self):
        """检查是否有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def auto_check_on_startup(self):
        """启动时自动检查"""
        if self.config["auto_check_updates"]:
            self.log_message("启动时自动检查...")
            threading.Thread(target=self.comprehensive_check, daemon=True).start()

    def comprehensive_check(self):
        """全面检查系统状态"""
        self.log_message("开始全面系统检查...")
        self.progress.start()

        def check_thread():
            try:
                # 检查Python版本
                python_ok = self.check_python_version()

                # 检查依赖
                deps_ok = self.check_dependencies()

                # 检查主程序
                program_ok = self.check_main_program()

                # 检查管理员权限
                admin_ok = self.is_admin()
                if admin_ok:
                    self.status_vars["admin"].set("✓ 已获得管理员权限")
                else:
                    self.status_vars["admin"].set("⚠ 建议以管理员身份运行")

                # 检查更新
                if self.config["auto_check_updates"]:
                    self.check_for_updates_silent()

                # 自动修复
                if self.config["auto_install_deps"] and not deps_ok:
                    self.log_message("自动安装缺失的依赖...")
                    self.install_dependencies_silent()

                # 更新按钮状态
                self.root.after(0, self.update_button_states, python_ok, deps_ok, program_ok)

            except Exception as e:
                self.log_message(f"系统检查出错: {e}", "ERROR")
            finally:
                self.root.after(0, self.progress.stop)

        threading.Thread(target=check_thread, daemon=True).start()

    def check_python_version(self):
        """检查Python版本"""
        try:
            version = sys.version_info
            min_version = tuple(self.config["python_min_version"])

            if version >= min_version:
                self.status_vars["python"].set(f"✓ Python {version.major}.{version.minor}.{version.micro}")
                self.log_message(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
                return True
            else:
                self.status_vars["python"].set(f"✗ 版本过低 ({version.major}.{version.minor})")
                self.log_message(f"Python版本过低: {version.major}.{version.minor}, 需要 {min_version}", "WARNING")
                return False
        except Exception as e:
            self.status_vars["python"].set(f"✗ 检查失败: {e}")
            self.log_message(f"Python版本检查失败: {e}", "ERROR")
            return False

    def check_dependencies(self):
        """检查依赖库"""
        missing_deps = []

        for dep in self.config["dependencies"]:
            try:
                __import__(dep.replace("-", "_"))
                self.log_message(f"依赖库 {dep} 检查通过")
            except ImportError:
                missing_deps.append(dep)
                self.log_message(f"缺少依赖库: {dep}", "WARNING")

        if not missing_deps:
            self.status_vars["dependencies"].set("✓ 所有依赖已安装")
            return True
        else:
            self.status_vars["dependencies"].set(f"✗ 缺少: {', '.join(missing_deps)}")
            return False

    def check_main_program(self):
        """检查主程序文件"""
        program_path = Path(self.config["main_program"])

        if program_path.exists():
            self.status_vars["program"].set("✓ 主程序存在")
            self.log_message(f"主程序文件检查通过: {program_path}")
            return True
        else:
            self.status_vars["program"].set("✗ 主程序不存在")
            self.log_message(f"主程序文件不存在: {program_path}", "ERROR")
            return False

    def update_button_states(self, python_ok, deps_ok, program_ok):
        """更新按钮状态"""
        # 启动按钮
        can_launch = python_ok and deps_ok and program_ok and not self.is_running
        self.launch_btn.config(state=tk.NORMAL if can_launch else tk.DISABLED)

        # 自动修复按钮
        needs_fix = not (python_ok and deps_ok and program_ok)
        self.auto_fix_btn.config(state=tk.NORMAL if needs_fix else tk.DISABLED)

        self.log_message("系统检查完成")

    def auto_fix_issues(self):
        """自动修复问题"""
        self.log_message("开始自动修复...")
        self.progress.start()

        def fix_thread():
            try:
                # 安装缺失的依赖
                missing_deps = []
                for dep in self.config["dependencies"]:
                    try:
                        __import__(dep.replace("-", "_"))
                    except ImportError:
                        missing_deps.append(dep)

                if missing_deps:
                    self.log_message("正在安装缺失的依赖...")
                    self.install_dependencies_silent()

                # 检查主程序
                if not Path(self.config["main_program"]).exists():
                    self.log_message("主程序文件不存在，尝试从备份恢复或重新下载...", "WARNING")
                    # 这里可以添加从备份恢复或重新下载的逻辑

                # 重新检查
                self.root.after(0, self.comprehensive_check)

            except Exception as e:
                self.log_message(f"自动修复过程出错: {e}", "ERROR")
            finally:
                self.root.after(0, self.progress.stop)

        threading.Thread(target=fix_thread, daemon=True).start()

    def install_dependencies_silent(self):
        """静默安装依赖"""
        for dep in self.config["dependencies"]:
            try:
                __import__(dep.replace("-", "_"))
            except ImportError:
                self.log_message(f"正在安装 {dep}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", dep, "--upgrade", "--quiet"
                ], capture_output=True, text=True, encoding='utf-8')

                if result.returncode == 0:
                    self.log_message(f"✓ {dep} 安装成功")
                else:
                    self.log_message(f"✗ {dep} 安装失败: {result.stderr}", "ERROR")

    def launch_program(self):
        """启动主程序"""
        if self.is_running:
            self.log_message("程序已在运行中", "WARNING")
            return

        # 检查是否需要管理员权限
        if self.config["launch_as_admin"] and not self.is_admin():
            self.restart_as_admin()
            return

        self.log_message("正在启动程序...")
        self.launch_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)

        def launch_thread():
            try:
                # 启动主程序
                self.process = subprocess.Popen([
                    sys.executable, self.config["main_program"]
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                   text=True, encoding='utf-8', cwd=os.getcwd())

                self.is_running = True
                self.log_message("程序启动成功")

                # 监控程序运行
                self.monitor_program()

            except Exception as e:
                self.log_message(f"启动失败: {e}", "ERROR")
                self.suggest_launch_solutions(str(e))
                self.root.after(0, lambda: [
                    self.launch_btn.config(state=tk.NORMAL),
                    self.stop_btn.config(state=tk.DISABLED)
                ])
                self.is_running = False

        threading.Thread(target=launch_thread, daemon=True).start()

    def monitor_program(self):
        """监控程序运行状态"""
        def monitor_thread():
            try:
                # 等待程序结束
                stdout, stderr = self.process.communicate()

                if self.process.returncode != 0:
                    self.log_message(f"程序异常退出 (代码: {self.process.returncode})", "ERROR")
                    if stderr:
                        self.log_message(f"错误信息: {stderr}", "ERROR")
                        self.analyze_error_and_suggest_solutions(stderr)
                else:
                    self.log_message("程序正常退出")

            except Exception as e:
                self.log_message(f"监控程序时出错: {e}", "ERROR")
            finally:
                self.is_running = False
                self.process = None
                self.root.after(0, lambda: [
                    self.launch_btn.config(state=tk.NORMAL),
                    self.stop_btn.config(state=tk.DISABLED)
                ])

        threading.Thread(target=monitor_thread, daemon=True).start()

    def stop_program(self):
        """停止程序"""
        if self.process and self.is_running:
            try:
                self.process.terminate()
                self.log_message("正在停止程序...")

                # 等待程序结束，如果超时则强制杀死
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.log_message("强制终止程序")

                self.is_running = False
                self.process = None
                self.launch_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
                self.log_message("程序已停止")

            except Exception as e:
                self.log_message(f"停止程序时出错: {e}", "ERROR")

    def restart_as_admin(self):
        """以管理员身份重启"""
        if self.is_admin():
            self.log_message("已经具有管理员权限", "INFO")
            return

        try:
            # 保存当前配置
            self.save_settings()

            # 以管理员身份重新启动
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, f'"{__file__}"', None, 1
            )
            self.root.quit()
        except Exception as e:
            self.log_message(f"以管理员身份重启失败: {e}", "ERROR")

    def check_for_updates(self):
        """检查更新"""
        self.log_message("正在检查更新...")
        self.progress.start()

        def check_thread():
            try:
                if self.config["github_repo"]:
                    # 从GitHub检查更新
                    self.check_github_updates()
                elif self.config["backup_url"]:
                    # 从备用URL检查更新
                    self.check_backup_url_updates()
                else:
                    self.log_message("未配置更新源", "WARNING")
                    self.status_vars["updates"].set("⚠ 未配置更新源")

            except Exception as e:
                self.log_message(f"检查更新失败: {e}", "ERROR")
                self.status_vars["updates"].set("✗ 检查失败")
            finally:
                self.root.after(0, self.progress.stop)

        threading.Thread(target=check_thread, daemon=True).start()

    def check_for_updates_silent(self):
        """静默检查更新"""
        try:
            current_time = time.time()
            if current_time - self.config["last_check"] < self.config["check_interval"]:
                self.status_vars["updates"].set("✓ 最近已检查")
                return

            self.config["last_check"] = current_time
            self.save_config()

            if self.config["github_repo"]:
                self.check_github_updates()
            else:
                self.status_vars["updates"].set("⚠ 未配置更新源")

        except Exception as e:
            self.log_message(f"静默检查更新失败: {e}", "ERROR")

    def check_github_updates(self):
        """从GitHub检查更新"""
        try:
            repo = self.config["github_repo"]
            if not repo:
                return

            # 获取最新release信息
            api_url = f"https://api.github.com/repos/{repo}/releases/latest"
            response = requests.get(api_url, timeout=10)

            if response.status_code == 200:
                release_info = response.json()
                latest_version = release_info["tag_name"]

                # 这里可以比较版本号
                self.log_message(f"最新版本: {latest_version}")
                self.status_vars["updates"].set(f"✓ 最新: {latest_version}")

                # 更新信息显示
                self.update_info_text.config(state=tk.NORMAL)
                self.update_info_text.delete(1.0, tk.END)
                self.update_info_text.insert(tk.END, f"最新版本: {latest_version}\n")
                self.update_info_text.insert(tk.END, f"发布时间: {release_info['published_at']}\n")
                self.update_info_text.insert(tk.END, f"更新说明:\n{release_info['body']}\n")
                self.update_info_text.config(state=tk.DISABLED)

                # 启用下载按钮
                self.download_update_btn.config(state=tk.NORMAL)

            else:
                self.log_message(f"检查更新失败: HTTP {response.status_code}", "ERROR")
                self.status_vars["updates"].set("✗ 检查失败")

        except Exception as e:
            self.log_message(f"GitHub更新检查失败: {e}", "ERROR")
            self.status_vars["updates"].set("✗ 检查失败")

    def download_update(self):
        """下载更新"""
        self.log_message("功能开发中...", "INFO")
        messagebox.showinfo("提示", "更新下载功能正在开发中")

    def install_update(self):
        """安装更新"""
        self.log_message("功能开发中...", "INFO")
        messagebox.showinfo("提示", "更新安装功能正在开发中")

    def backup_program(self):
        """备份程序"""
        try:
            backup_dir = filedialog.askdirectory(title="选择备份目录")
            if not backup_dir:
                return

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_name = f"game_macro_backup_{timestamp}"
            backup_path = os.path.join(backup_dir, backup_name)

            self.log_message(f"正在备份到: {backup_path}")

            # 创建备份目录
            os.makedirs(backup_path, exist_ok=True)

            # 备份主要文件
            files_to_backup = [
                "game_macro_gui.py",
                "game_macro.py",
                "direct_input_helper.py",
                "README.md"
            ]

            for file in files_to_backup:
                if os.path.exists(file):
                    shutil.copy2(file, backup_path)
                    self.log_message(f"已备份: {file}")

            self.log_message(f"备份完成: {backup_path}")
            messagebox.showinfo("成功", f"程序已备份到:\n{backup_path}")

        except Exception as e:
            self.log_message(f"备份失败: {e}", "ERROR")
            messagebox.showerror("错误", f"备份失败: {e}")

    def show_help(self):
        """显示帮助"""
        help_window = tk.Toplevel(self.root)
        help_window.title("帮助")
        help_window.geometry("600x400")

        help_text = scrolledtext.ScrolledText(help_window, wrap=tk.WORD)
        help_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        help_content = """
游戏宏工具高级启动器 - 帮助文档

=== 主要功能 ===

1. 🔍 全面检查
   - 检查Python环境
   - 检查依赖库
   - 检查主程序文件
   - 检查管理员权限
   - 检查更新状态

2. 🔧 自动修复
   - 自动安装缺失的依赖库
   - 尝试修复常见问题
   - 提供解决方案建议

3. 🚀 启动程序
   - 智能启动主程序
   - 监控程序运行状态
   - 检测错误并提供解决方案

4. 📥 更新管理
   - 检查程序更新
   - 自动下载和安装更新
   - 支持GitHub和自定义更新源

=== 常见问题解决 ===

Q: 程序无法启动？
A: 1. 点击"全面检查"查看系统状态
   2. 点击"自动修复"尝试解决问题
   3. 确保以管理员身份运行

Q: 依赖库安装失败？
A: 1. 检查网络连接
   2. 尝试以管理员身份运行
   3. 手动安装: pip install 库名

Q: 程序运行出错？
A: 1. 查看运行日志中的错误信息
   2. 根据错误提示进行相应操作
   3. 导出诊断报告寻求帮助

=== 设置说明 ===

- 主程序文件: 指定要启动的Python程序
- 自动检查更新: 启动时自动检查更新
- 自动安装依赖: 发现缺失依赖时自动安装
- 管理员启动: 总是以管理员身份启动程序

=== 联系支持 ===

如果遇到问题，请：
1. 导出诊断报告
2. 保存运行日志
3. 联系技术支持
        """

        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)

    def collect_system_info(self):
        """收集系统信息"""
        self.log_message("正在收集系统信息...")

        def collect_thread():
            try:
                import platform
                import psutil

                info = []
                info.append(f"操作系统: {platform.system()} {platform.release()}")
                info.append(f"处理器: {platform.processor()}")
                info.append(f"Python版本: {platform.python_version()}")
                info.append(f"内存: {psutil.virtual_memory().total // (1024**3)} GB")
                info.append(f"磁盘空间: {psutil.disk_usage('/').free // (1024**3)} GB 可用")

                # 检查已安装的包
                result = subprocess.run([sys.executable, "-m", "pip", "list"],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    info.append("\n已安装的包:")
                    info.append(result.stdout)

                system_info = "\n".join(info)

                self.system_info_text.config(state=tk.NORMAL)
                self.system_info_text.delete(1.0, tk.END)
                self.system_info_text.insert(tk.END, system_info)
                self.system_info_text.config(state=tk.DISABLED)

                self.log_message("系统信息收集完成")

            except Exception as e:
                self.log_message(f"收集系统信息失败: {e}", "ERROR")

        threading.Thread(target=collect_thread, daemon=True).start()

    def test_dependencies(self):
        """测试依赖库"""
        self.log_message("正在测试依赖库...")

        for dep in self.config["dependencies"]:
            try:
                module = __import__(dep.replace("-", "_"))
                version = getattr(module, "__version__", "未知版本")
                self.log_message(f"✓ {dep} - 版本: {version}")
            except ImportError:
                self.log_message(f"✗ {dep} - 未安装", "ERROR")
            except Exception as e:
                self.log_message(f"⚠ {dep} - 测试失败: {e}", "WARNING")

    def check_permissions(self):
        """检查权限"""
        self.log_message("正在检查权限...")

        # 检查当前目录权限
        current_dir = os.getcwd()
        try:
            test_file = os.path.join(current_dir, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            self.log_message(f"✓ 当前目录可写: {current_dir}")
        except Exception as e:
            self.log_message(f"✗ 当前目录不可写: {e}", "ERROR")

        # 检查管理员权限
        if self.is_admin():
            self.log_message("✓ 具有管理员权限")
        else:
            self.log_message("⚠ 没有管理员权限", "WARNING")

    def test_network(self):
        """测试网络连接"""
        self.log_message("正在测试网络连接...")

        test_urls = [
            "https://www.google.com",
            "https://pypi.org",
            "https://github.com"
        ]

        for url in test_urls:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    self.log_message(f"✓ {url} - 连接正常")
                else:
                    self.log_message(f"⚠ {url} - HTTP {response.status_code}", "WARNING")
            except Exception as e:
                self.log_message(f"✗ {url} - 连接失败: {e}", "ERROR")

    def export_diagnostic_report(self):
        """导出诊断报告"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            title="导出诊断报告"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("=== 游戏宏工具诊断报告 ===\n")
                    f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                    f.write("=== 系统状态 ===\n")
                    for key, var in self.status_vars.items():
                        f.write(f"{key}: {var.get()}\n")

                    f.write("\n=== 运行日志 ===\n")
                    f.write(self.log_text.get(1.0, tk.END))

                    f.write("\n=== 系统信息 ===\n")
                    f.write(self.system_info_text.get(1.0, tk.END))

                    f.write("\n=== 配置信息 ===\n")
                    f.write(json.dumps(self.config, ensure_ascii=False, indent=2))

                self.log_message(f"诊断报告已导出: {filename}")
                messagebox.showinfo("成功", f"诊断报告已导出到:\n{filename}")

            except Exception as e:
                self.log_message(f"导出诊断报告失败: {e}", "ERROR")
                messagebox.showerror("错误", f"导出失败: {e}")

    def browse_main_program(self):
        """浏览主程序文件"""
        filename = filedialog.askopenfilename(
            filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")],
            title="选择主程序文件"
        )
        if filename:
            self.main_program_var.set(filename)

    def save_settings(self):
        """保存设置"""
        try:
            self.config["main_program"] = self.main_program_var.get()
            self.config["auto_check_updates"] = self.auto_check_var.get()
            self.config["auto_install_deps"] = self.auto_install_var.get()
            self.config["launch_as_admin"] = self.launch_admin_var.get()
            self.config["github_repo"] = self.github_repo_var.get()
            self.config["backup_url"] = self.backup_url_var.get()

            self.save_config()
            self.log_message("设置已保存")
            messagebox.showinfo("成功", "设置已保存")

        except Exception as e:
            self.log_message(f"保存设置失败: {e}", "ERROR")
            messagebox.showerror("错误", f"保存设置失败: {e}")

    def analyze_error_and_suggest_solutions(self, error_message):
        """分析错误并提供解决方案"""
        error_lower = error_message.lower()

        # 常见错误模式和解决方案
        error_patterns = {
            "modulenotfounderror": {
                "description": "缺少必需的模块",
                "solutions": [
                    "点击'自动修复'按钮安装缺失的依赖",
                    "手动运行: pip install 模块名",
                    "检查Python环境是否正确配置"
                ]
            },
            "permission": {
                "description": "权限不足",
                "solutions": [
                    "点击'管理员重启'按钮",
                    "确保程序文件夹有写入权限",
                    "关闭可能阻止程序运行的安全软件"
                ]
            },
            "access is denied": {
                "description": "访问被拒绝",
                "solutions": [
                    "以管理员身份运行程序",
                    "检查防病毒软件是否阻止了程序",
                    "确保文件没有被其他程序占用"
                ]
            }
        }

        self.log_message("=== 错误分析和解决方案 ===", "SOLUTION")

        found_solution = False
        for pattern, info in error_patterns.items():
            if pattern in error_lower:
                self.log_message(f"问题类型: {info['description']}", "SOLUTION")
                self.log_message("建议解决方案:", "SOLUTION")
                for i, solution in enumerate(info['solutions'], 1):
                    self.log_message(f"  {i}. {solution}", "SOLUTION")
                found_solution = True
                break

        if not found_solution:
            self.log_message("通用解决方案建议:", "SOLUTION")
            self.log_message("  1. 检查所有依赖是否正确安装", "SOLUTION")
            self.log_message("  2. 以管理员身份运行程序", "SOLUTION")
            self.log_message("  3. 检查防病毒软件设置", "SOLUTION")
            self.log_message("  4. 重启计算机后再试", "SOLUTION")

    def suggest_launch_solutions(self, error_message):
        """为启动问题提供解决方案"""
        self.log_message("=== 启动失败解决方案 ===", "SOLUTION")
        self.log_message("  1. 确保所有依赖库已正确安装", "SOLUTION")
        self.log_message("  2. 检查主程序文件是否存在且完整", "SOLUTION")
        self.log_message("  3. 尝试以管理员身份运行", "SOLUTION")
        self.log_message("  4. 检查Python环境是否正确", "SOLUTION")
        self.log_message("  5. 查看详细错误信息并搜索解决方案", "SOLUTION")


def main():
    """主函数"""
    try:
        app = AdvancedLauncher()
        app.root.mainloop()
    except Exception as e:
        print(f"高级启动器发生错误: {e}")
        messagebox.showerror("错误", f"高级启动器发生错误: {e}")


if __name__ == "__main__":
    main()
