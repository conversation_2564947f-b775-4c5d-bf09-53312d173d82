import time
import json
import threading
import ctypes
import sys
from pynput import mouse, keyboard
from pynput.mouse import <PERSON><PERSON>, Controller as Mouse<PERSON>ontroller
from pynput.keyboard import Key, KeyC<PERSON>, Controller as KeyboardController
import win32api
import win32con
# 导入DirectInput辅助模块
from direct_input_helper import DirectInputHelper

# 自定义Windows低级输入模拟
user32 = ctypes.windll.user32
MOUSEEVENTF_MOVE = 0x0001
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
MOUSEEVENTF_RIGHTDOWN = 0x0008
MOUSEEVENTF_RIGHTUP = 0x0010
MOUSEEVENTF_MIDDLEDOWN = 0x0020
MOUSEEVENTF_MIDDLEUP = 0x0040
MOUSEEVENTF_WHEEL = 0x0800
MOUSEEVENTF_ABSOLUTE = 0x8000

# 用于存储录制的事件
class MacroRecorder:
    def __init__(self):
        self.events = []
        self.recording = False
        self.start_time = 0
        self.keyboard_listener = None
        self.mouse_listener = None
        self.mouse_ctrl = MouseController()
        self.keyboard_ctrl = KeyboardController()
        # 增加使用DirectInput模式的标志
        self.use_direct_input = True
        
    def on_press(self, key):
        if not self.recording:
            return
        try:
            # 记录按键
            current_time = time.time() - self.start_time
            if hasattr(key, 'char'):
                if key.char is not None:
                    self.events.append({
                        'type': 'key_press',
                        'key': key.char,
                        'time': current_time
                    })
            else:
                # 特殊键
                key_name = str(key).replace('Key.', '')
                self.events.append({
                    'type': 'key_press',
                    'key': key_name,
                    'time': current_time
                })
        except Exception as e:
            print(f"按键记录错误: {e}")
    
    def on_release(self, key):
        if not self.recording:
            return
        try:
            current_time = time.time() - self.start_time
            if hasattr(key, 'char'):
                if key.char is not None:
                    self.events.append({
                        'type': 'key_release',
                        'key': key.char,
                        'time': current_time
                    })
            else:
                key_name = str(key).replace('Key.', '')
                self.events.append({
                    'type': 'key_release',
                    'key': key_name,
                    'time': current_time
                })
            
            # 检查停止录制的热键 (F8)
            if key == Key.f8:
                return False
        except Exception as e:
            print(f"按键释放记录错误: {e}")
            
    def on_move(self, x, y):
        if not self.recording:
            return
        current_time = time.time() - self.start_time
        self.events.append({
            'type': 'mouse_move',
            'x': x,
            'y': y,
            'time': current_time
        })
        
    def on_click(self, x, y, button, pressed):
        if not self.recording:
            return
        current_time = time.time() - self.start_time
        btn = 'left'
        if button == Button.right:
            btn = 'right'
        elif button == Button.middle:
            btn = 'middle'
            
        action = 'press' if pressed else 'release'
        self.events.append({
            'type': f'mouse_{action}',
            'button': btn,
            'x': x,
            'y': y,
            'time': current_time
        })
        
    def on_scroll(self, x, y, dx, dy):
        if not self.recording:
            return
        current_time = time.time() - self.start_time
        self.events.append({
            'type': 'mouse_scroll',
            'x': x,
            'y': y,
            'dx': dx,
            'dy': dy,
            'time': current_time
        })
        
    def start_recording(self):
        # 清除之前的记录
        self.events = []
        self.recording = True
        self.start_time = time.time()
        
        # 启动键盘和鼠标监听器
        self.keyboard_listener = keyboard.Listener(
            on_press=self.on_press,
            on_release=self.on_release)
        self.mouse_listener = mouse.Listener(
            on_move=self.on_move,
            on_click=self.on_click,
            on_scroll=self.on_scroll)
            
        self.keyboard_listener.start()
        self.mouse_listener.start()
        print("开始录制 (按F8停止)...")
        self.keyboard_listener.join()
        self.recording = False
        self.mouse_listener.stop()
        print(f"录制完成, 共记录了 {len(self.events)} 个事件")
        
    def save_macro(self, filename):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.events, f, ensure_ascii=False, indent=2)
        print(f"宏保存到 {filename}")
        
    def load_macro(self, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.events = json.load(f)
            print(f"从 {filename} 加载了 {len(self.events)} 个事件")
            return True
        except Exception as e:
            print(f"加载宏出错: {e}")
            return False
    
    # 使用Windows API直接发送输入，更适合在游戏中使用
    def playback_macro(self):
        if not self.events:
            print("没有录制的宏可以回放")
            return
            
        print("准备开始回放，3秒后开始...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
            
        print("开始回放...")
        start_time = time.time()
        last_event_time = 0
        
        for event in self.events:
            # 计算需要等待的时间
            current_time = time.time() - start_time
            time_to_wait = event['time'] - current_time
            if time_to_wait > 0:
                time.sleep(time_to_wait)
                
            # 根据事件类型执行相应的操作
            if event['type'] == 'key_press':
                self._simulate_key(event['key'], True)
            elif event['type'] == 'key_release':
                self._simulate_key(event['key'], False)
            elif event['type'] == 'mouse_move':
                self._simulate_mouse_move(event['x'], event['y'])
            elif event['type'] == 'mouse_press':
                self._simulate_mouse_button(event['button'], True, event['x'], event['y'])
            elif event['type'] == 'mouse_release':
                self._simulate_mouse_button(event['button'], False, event['x'], event['y'])
            elif event['type'] == 'mouse_scroll':
                self._simulate_mouse_scroll(event['dy'])
                
        print("宏回放完成")
    
    def _get_virtual_keycode(self, key):
        # 特殊键映射
        special_keys = {
            'alt': win32con.VK_MENU,
            'alt_l': win32con.VK_LMENU,
            'alt_r': win32con.VK_RMENU,
            'alt_gr': win32con.VK_RMENU,
            'backspace': win32con.VK_BACK,
            'caps_lock': win32con.VK_CAPITAL,
            'cmd': win32con.VK_LWIN,
            'cmd_l': win32con.VK_LWIN,
            'cmd_r': win32con.VK_RWIN,
            'ctrl': win32con.VK_CONTROL,
            'ctrl_l': win32con.VK_LCONTROL,
            'ctrl_r': win32con.VK_RCONTROL,
            'delete': win32con.VK_DELETE,
            'down': win32con.VK_DOWN,
            'end': win32con.VK_END,
            'enter': win32con.VK_RETURN,
            'esc': win32con.VK_ESCAPE,
            'f1': win32con.VK_F1,
            'f2': win32con.VK_F2,
            'f3': win32con.VK_F3,
            'f4': win32con.VK_F4,
            'f5': win32con.VK_F5,
            'f6': win32con.VK_F6,
            'f7': win32con.VK_F7,
            'f8': win32con.VK_F8,
            'f9': win32con.VK_F9,
            'f10': win32con.VK_F10,
            'f11': win32con.VK_F11,
            'f12': win32con.VK_F12,
            'home': win32con.VK_HOME,
            'insert': win32con.VK_INSERT,
            'left': win32con.VK_LEFT,
            'page_down': win32con.VK_NEXT,
            'page_up': win32con.VK_PRIOR,
            'right': win32con.VK_RIGHT,
            'shift': win32con.VK_SHIFT,
            'shift_l': win32con.VK_LSHIFT,
            'shift_r': win32con.VK_RSHIFT,
            'space': win32con.VK_SPACE,
            'tab': win32con.VK_TAB,
            'up': win32con.VK_UP
        }
        
        # 如果是特殊键
        if key in special_keys:
            return special_keys[key]
        # 如果是单个字符
        elif len(key) == 1:
            # 字母和数字
            return ord(key.upper())
        
        return None
        
    def _simulate_key(self, key, press):
        # 优先使用DirectInput方式
        if self.use_direct_input:
            if DirectInputHelper.send_key_event_direct(key, press):
                return
                
        # 如果DirectInput方式失败，回退到传统方式
        key_code = self._get_virtual_keycode(key)
        if key_code:
            if press:
                win32api.keybd_event(key_code, 0, 0, 0)
            else:
                win32api.keybd_event(key_code, 0, win32con.KEYEVENTF_KEYUP, 0)
    
    def _simulate_mouse_move(self, x, y):
        if self.use_direct_input:
            DirectInputHelper.set_cursor_position(x, y)
        else:
            # 将x,y坐标转换为屏幕绝对坐标
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)
            
            # 计算规范化的坐标 (0-65535)
            nx = int(65535 * x / screen_width)
            ny = int(65535 * y / screen_height)
            
            user32.mouse_event(MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_MOVE, nx, ny, 0, 0)
    
    def _simulate_mouse_button(self, button, press, x, y):
        if self.use_direct_input:
            DirectInputHelper.send_mouse_input_direct(x, y, button, press)
        else:
            # 首先移动鼠标到指定位置
            self._simulate_mouse_move(x, y)
            
            # 然后执行按钮操作
            if button == 'left':
                if press:
                    user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                else:
                    user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            elif button == 'right':
                if press:
                    user32.mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0)
                else:
                    user32.mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0)
            elif button == 'middle':
                if press:
                    user32.mouse_event(MOUSEEVENTF_MIDDLEDOWN, 0, 0, 0, 0)
                else:
                    user32.mouse_event(MOUSEEVENTF_MIDDLEUP, 0, 0, 0, 0)
    
    def _simulate_mouse_scroll(self, dy):
        user32.mouse_event(MOUSEEVENTF_WHEEL, 0, 0, int(dy * 120), 0)
        
# 创建简单的命令行界面
def main():
    recorder = MacroRecorder()
    
    while True:
        print("\n=== 游戏宏录制与回放工具 ===")
        print("1. 开始录制 (按F8停止)")
        print("2. 回放录制的宏")
        print("3. 保存宏")
        print("4. 加载宏")
        print("5. 切换输入模式 (当前: " + ("DirectInput" if recorder.use_direct_input else "标准模式") + ")")
        print("6. 退出")
        
        choice = input("请选择操作 (1-6): ")
        
        if choice == '1':
            print("将在3秒后开始录制...")
            for i in range(3, 0, -1):
                print(f"{i}...")
                time.sleep(1)
            recorder.start_recording()
        elif choice == '2':
            recorder.playback_macro()
        elif choice == '3':
            filename = input("请输入保存的文件名: ")
            if not filename.endswith('.json'):
                filename += '.json'
            recorder.save_macro(filename)
        elif choice == '4':
            filename = input("请输入要加载的文件名: ")
            if not filename.endswith('.json'):
                filename += '.json'
            recorder.load_macro(filename)
        elif choice == '5':
            recorder.use_direct_input = not recorder.use_direct_input
            print(f"输入模式已切换为: {('DirectInput' if recorder.use_direct_input else '标准模式')}")
            print("注意: 如果在游戏中无效，请尝试切换输入模式")
        elif choice == '6':
            print("谢谢使用，再见!")
            break
        else:
            print("无效的选择，请重新输入。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序已被用户中断")
    except Exception as e:
        print(f"发生错误: {e}") 