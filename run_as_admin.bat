@echo off
echo 正在以管理员权限启动游戏宏录制工具...

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Python安装，请先安装Python
    pause
    exit /b 1
)

:: 检查必要的库是否安装
echo 检查必要的库...
pip show pynput >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装必要的库: pynput
    pip install pynput
)

pip show pywin32 >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装必要的库: pywin32
    pip install pywin32
)

:: 设置权限提升的VBS脚本
echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
echo UAC.ShellExecute "python", "game_macro.py", "", "runas", 1 >> "%temp%\getadmin.vbs"

:: 运行VBS脚本
echo 启动游戏宏录制工具（以管理员权限）...
"%temp%\getadmin.vbs"
del "%temp%\getadmin.vbs"

exit /b 0 