#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能程序启动器
自动检测依赖、启动程序、检测错误并提供解决方案
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import requests
from pathlib import Path
import ctypes
import winreg
import shutil

class SmartLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能程序启动器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 程序配置
        self.config = {
            "main_program": "game_macro_gui.py",
            "dependencies": ["pynput", "pywin32"],
            "python_min_version": (3, 7),
            "github_repo": None,  # 如果有GitHub仓库可以在这里设置
            "backup_url": None    # 备用下载链接
        }
        
        # 状态变量
        self.is_running = False
        self.process = None
        
        self.create_widgets()
        self.check_system_requirements()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="游戏宏工具智能启动器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 状态框架
        status_frame = ttk.LabelFrame(main_frame, text="系统状态")
        status_frame.pack(fill=tk.X, pady=5)
        
        # 状态指示器
        self.status_vars = {
            "python": tk.StringVar(value="检查中..."),
            "dependencies": tk.StringVar(value="检查中..."),
            "program": tk.StringVar(value="检查中..."),
            "admin": tk.StringVar(value="检查中...")
        }
        
        status_items = [
            ("Python环境", "python"),
            ("依赖库", "dependencies"), 
            ("主程序", "program"),
            ("管理员权限", "admin")
        ]
        
        for i, (label, key) in enumerate(status_items):
            frame = ttk.Frame(status_frame)
            frame.pack(fill=tk.X, padx=5, pady=2)
            ttk.Label(frame, text=f"{label}:", width=12).pack(side=tk.LEFT)
            status_label = ttk.Label(frame, textvariable=self.status_vars[key])
            status_label.pack(side=tk.LEFT)
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        # 按钮
        self.check_btn = ttk.Button(control_frame, text="重新检查", 
                                   command=self.check_system_requirements)
        self.check_btn.pack(side=tk.LEFT, padx=5)
        
        self.install_btn = ttk.Button(control_frame, text="安装依赖", 
                                     command=self.install_dependencies)
        self.install_btn.pack(side=tk.LEFT, padx=5)
        
        self.launch_btn = ttk.Button(control_frame, text="启动程序", 
                                    command=self.launch_program)
        self.launch_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止程序", 
                                  command=self.stop_program, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        self.admin_btn = ttk.Button(control_frame, text="以管理员身份重启", 
                                   command=self.restart_as_admin)
        self.admin_btn.pack(side=tk.LEFT, padx=5)
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="运行日志")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, 
                                                 height=15, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update()
    
    def is_admin(self):
        """检查是否有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def check_python_version(self):
        """检查Python版本"""
        try:
            version = sys.version_info
            min_version = self.config["python_min_version"]
            
            if version >= min_version:
                self.status_vars["python"].set(f"✓ Python {version.major}.{version.minor}")
                return True
            else:
                self.status_vars["python"].set(f"✗ 版本过低 ({version.major}.{version.minor})")
                return False
        except Exception as e:
            self.status_vars["python"].set(f"✗ 检查失败: {e}")
            return False
    
    def check_dependencies(self):
        """检查依赖库"""
        missing_deps = []
        
        for dep in self.config["dependencies"]:
            try:
                __import__(dep.replace("-", "_"))
            except ImportError:
                missing_deps.append(dep)
        
        if not missing_deps:
            self.status_vars["dependencies"].set("✓ 所有依赖已安装")
            return True
        else:
            self.status_vars["dependencies"].set(f"✗ 缺少: {', '.join(missing_deps)}")
            return False
    
    def check_main_program(self):
        """检查主程序文件"""
        program_path = Path(self.config["main_program"])
        
        if program_path.exists():
            self.status_vars["program"].set("✓ 主程序存在")
            return True
        else:
            self.status_vars["program"].set("✗ 主程序不存在")
            return False
    
    def check_system_requirements(self):
        """检查系统要求"""
        self.log_message("开始系统检查...")
        self.progress.start()
        
        def check_thread():
            try:
                # 检查Python版本
                python_ok = self.check_python_version()
                
                # 检查依赖
                deps_ok = self.check_dependencies()
                
                # 检查主程序
                program_ok = self.check_main_program()
                
                # 检查管理员权限
                admin_ok = self.is_admin()
                if admin_ok:
                    self.status_vars["admin"].set("✓ 已获得管理员权限")
                else:
                    self.status_vars["admin"].set("⚠ 建议以管理员身份运行")
                
                # 更新按钮状态
                self.root.after(0, self.update_button_states, python_ok, deps_ok, program_ok)
                
            except Exception as e:
                self.log_message(f"系统检查出错: {e}", "ERROR")
            finally:
                self.root.after(0, self.progress.stop)
        
        threading.Thread(target=check_thread, daemon=True).start()
    
    def update_button_states(self, python_ok, deps_ok, program_ok):
        """更新按钮状态"""
        # 安装按钮
        self.install_btn.config(state=tk.NORMAL if not deps_ok else tk.DISABLED)
        
        # 启动按钮
        can_launch = python_ok and deps_ok and program_ok and not self.is_running
        self.launch_btn.config(state=tk.NORMAL if can_launch else tk.DISABLED)
        
        self.log_message("系统检查完成")
    
    def install_dependencies(self):
        """安装依赖库"""
        self.log_message("开始安装依赖库...")
        self.progress.start()
        self.install_btn.config(state=tk.DISABLED)
        
        def install_thread():
            try:
                for dep in self.config["dependencies"]:
                    self.log_message(f"正在安装 {dep}...")
                    
                    # 使用pip安装
                    result = subprocess.run([
                        sys.executable, "-m", "pip", "install", dep, "--upgrade"
                    ], capture_output=True, text=True, encoding='utf-8')
                    
                    if result.returncode == 0:
                        self.log_message(f"✓ {dep} 安装成功")
                    else:
                        self.log_message(f"✗ {dep} 安装失败: {result.stderr}", "ERROR")
                        
                        # 尝试解决常见问题
                        self.suggest_dependency_solutions(dep, result.stderr)
                
                # 重新检查依赖
                self.root.after(0, self.check_system_requirements)
                
            except Exception as e:
                self.log_message(f"安装过程出错: {e}", "ERROR")
            finally:
                self.root.after(0, lambda: [
                    self.progress.stop(),
                    self.install_btn.config(state=tk.NORMAL)
                ])
        
        threading.Thread(target=install_thread, daemon=True).start()

    def suggest_dependency_solutions(self, dependency, error_message):
        """为依赖安装问题提供解决方案"""
        solutions = {
            "pynput": [
                "尝试安装Microsoft Visual C++ Redistributable",
                "确保Windows版本支持该库",
                "尝试使用: pip install pynput --no-cache-dir"
            ],
            "pywin32": [
                "尝试使用: pip install pywin32 --upgrade",
                "如果失败，尝试: pip install pypiwin32",
                "确保Python版本与pywin32兼容"
            ]
        }

        if dependency in solutions:
            self.log_message(f"解决方案建议 ({dependency}):", "SOLUTION")
            for solution in solutions[dependency]:
                self.log_message(f"  • {solution}", "SOLUTION")

    def launch_program(self):
        """启动主程序"""
        if self.is_running:
            self.log_message("程序已在运行中", "WARNING")
            return

        self.log_message("正在启动程序...")
        self.launch_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)

        def launch_thread():
            try:
                # 启动主程序
                self.process = subprocess.Popen([
                    sys.executable, self.config["main_program"]
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                   text=True, encoding='utf-8')

                self.is_running = True
                self.log_message("程序启动成功")

                # 监控程序运行
                self.monitor_program()

            except Exception as e:
                self.log_message(f"启动失败: {e}", "ERROR")
                self.suggest_launch_solutions(str(e))
                self.root.after(0, lambda: [
                    self.launch_btn.config(state=tk.NORMAL),
                    self.stop_btn.config(state=tk.DISABLED)
                ])
                self.is_running = False

        threading.Thread(target=launch_thread, daemon=True).start()

    def monitor_program(self):
        """监控程序运行状态"""
        def monitor_thread():
            try:
                # 等待程序结束
                stdout, stderr = self.process.communicate()

                if self.process.returncode != 0:
                    self.log_message(f"程序异常退出 (代码: {self.process.returncode})", "ERROR")
                    if stderr:
                        self.log_message(f"错误信息: {stderr}", "ERROR")
                        self.analyze_error_and_suggest_solutions(stderr)
                else:
                    self.log_message("程序正常退出")

            except Exception as e:
                self.log_message(f"监控程序时出错: {e}", "ERROR")
            finally:
                self.is_running = False
                self.process = None
                self.root.after(0, lambda: [
                    self.launch_btn.config(state=tk.NORMAL),
                    self.stop_btn.config(state=tk.DISABLED)
                ])

        threading.Thread(target=monitor_thread, daemon=True).start()

    def stop_program(self):
        """停止程序"""
        if self.process and self.is_running:
            try:
                self.process.terminate()
                self.log_message("正在停止程序...")

                # 等待程序结束，如果超时则强制杀死
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.log_message("强制终止程序")

                self.is_running = False
                self.process = None
                self.launch_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
                self.log_message("程序已停止")

            except Exception as e:
                self.log_message(f"停止程序时出错: {e}", "ERROR")

    def restart_as_admin(self):
        """以管理员身份重启"""
        if self.is_admin():
            self.log_message("已经具有管理员权限", "INFO")
            return

        try:
            # 以管理员身份重新启动
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, f'"{__file__}"', None, 1
            )
            self.root.quit()
        except Exception as e:
            self.log_message(f"以管理员身份重启失败: {e}", "ERROR")

    def analyze_error_and_suggest_solutions(self, error_message):
        """分析错误并提供解决方案"""
        error_lower = error_message.lower()

        # 常见错误模式和解决方案
        error_patterns = {
            "modulenotfounderror": {
                "description": "缺少必需的模块",
                "solutions": [
                    "运行'安装依赖'按钮重新安装所需库",
                    "检查Python环境是否正确配置",
                    "尝试使用虚拟环境"
                ]
            },
            "permission": {
                "description": "权限不足",
                "solutions": [
                    "点击'以管理员身份重启'按钮",
                    "确保程序文件夹有写入权限",
                    "关闭可能阻止程序运行的安全软件"
                ]
            },
            "access is denied": {
                "description": "访问被拒绝",
                "solutions": [
                    "以管理员身份运行程序",
                    "检查防病毒软件是否阻止了程序",
                    "确保文件没有被其他程序占用"
                ]
            },
            "dll": {
                "description": "动态链接库问题",
                "solutions": [
                    "安装Microsoft Visual C++ Redistributable",
                    "更新Windows系统",
                    "重新安装Python和相关库"
                ]
            },
            "encoding": {
                "description": "编码问题",
                "solutions": [
                    "确保系统区域设置正确",
                    "尝试使用UTF-8编码",
                    "检查文件路径是否包含特殊字符"
                ]
            }
        }

        self.log_message("错误分析和解决方案:", "SOLUTION")

        found_solution = False
        for pattern, info in error_patterns.items():
            if pattern in error_lower:
                self.log_message(f"问题类型: {info['description']}", "SOLUTION")
                self.log_message("建议解决方案:", "SOLUTION")
                for i, solution in enumerate(info['solutions'], 1):
                    self.log_message(f"  {i}. {solution}", "SOLUTION")
                found_solution = True
                break

        if not found_solution:
            self.log_message("未找到特定解决方案，建议:", "SOLUTION")
            self.log_message("  1. 检查所有依赖是否正确安装", "SOLUTION")
            self.log_message("  2. 以管理员身份运行程序", "SOLUTION")
            self.log_message("  3. 检查防病毒软件设置", "SOLUTION")
            self.log_message("  4. 重启计算机后再试", "SOLUTION")

    def suggest_launch_solutions(self, error_message):
        """为启动问题提供解决方案"""
        self.log_message("启动失败解决方案:", "SOLUTION")
        self.log_message("  1. 确保所有依赖库已正确安装", "SOLUTION")
        self.log_message("  2. 检查主程序文件是否存在且完整", "SOLUTION")
        self.log_message("  3. 尝试以管理员身份运行", "SOLUTION")
        self.log_message("  4. 检查Python环境是否正确", "SOLUTION")


def main():
    """主函数"""
    try:
        app = SmartLauncher()
        app.root.mainloop()
    except Exception as e:
        print(f"启动器发生错误: {e}")
        messagebox.showerror("错误", f"启动器发生错误: {e}")


if __name__ == "__main__":
    main()
